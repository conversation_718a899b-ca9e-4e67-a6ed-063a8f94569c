import {
  FunctionComponent,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import PageProps from "../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Grid from "@mui/material/Grid";
import { Divider, Typography } from "@mui/material";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import { useDispatch, useSelector } from "react-redux";
import { OUTLINED_INPUT_STYLES } from "../../constants/styles.constant";

//Css Import
import "../analytics/gmbReports.screen.style.css";

// Image imports
import picture1Image from "../../assets/dashboard/Picture1.png";
import picture2Image from "../../assets/dashboard/Picture2.png";
import picture3Image from "../../assets/dashboard/Picture3.png";

import HomeChartCard from "../../components/homeChartCard/homeChartCard.component";
import RevenueChartDashboard from "../../components/revenueChartDashboard/revenueChartDashboard.component";
import DateFilter from "../../components/dateFilter/dateFilter.component";
import PlatformBreakdownChart from "../dashboardV2/platformBreakdownChart";
import LocationMetricsService from "../../services/locationMetrics/locationMetrics.service";
import { ILocationMetricsRequestModel } from "../../interfaces/request/ILocationMetricsRequestModel";
import dayjs from "dayjs";
import WebsiteClicksChart from "../dashboardV2/websiteClicksChart";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import ApplicationHelperService from "../../services/ApplicationHelperService";
import BusinessService from "../../services/business/business.service";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../interfaces/response/ILocationsListResponseModel";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../interfaces/response/IBusinessListResponseModel";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../interfaces/response/IBusinessGroupsResponseModel";
import { Formik, getIn } from "formik";
import * as yup from "yup";
import {
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  SelectChangeEvent,
  OutlinedInput,
  Chip,
  Button,
  Checkbox,
  ListItemText,
} from "@mui/material";
import { LoadingContext } from "../../context/loading.context";
import AnalyticsIcon from "@mui/icons-material/Analytics";
import LocationChips from "../../components/locationChips/locationChips.component";
import {
  ExcelExportService,
  ChartExportData,
} from "../../services/excelExport.service";
import FileDownloadIcon from "@mui/icons-material/FileDownload";

interface EventCounts {
  type: string;
  total: number;
}

interface IGraphDataModel {
  data: number[];
  labels: string[];
}

interface IAggregatedAnalyticsData {
  multiDailyMetricTimeSeries: Array<{
    dailyMetricTimeSeries: any[];
  }>;
  totalLocations: number;
  processedLocations: number;
}

interface IAnalyticsFilterModel {
  businessId: number;
  businessGroupId: string;
  locationIds: string[];
}

const GMBReports: FunctionComponent<PageProps> = () => {
  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  // Dropdown data states
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);
  const [locationList, setLocationList] = useState<ILocation[]>([]);

  const { setLoading } = useContext(LoadingContext);
  const _businessService = new BusinessService(dispatch);
  const _locationMetricsService = new LocationMetricsService(dispatch);
  const _applicationHelperService = new ApplicationHelperService(dispatch);
  const { setToastConfig } = useContext(ToastContext);

  // Form initial values
  const INITIAL_VALUES: IAnalyticsFilterModel = {
    businessId: 0,
    businessGroupId: "0",
    locationIds: [],
  };

  const [selectedDateRange, setSelectedDateRange] = useState<{
    from: string;
    to: string;
    isSameMonthYear: boolean;
  } | null>(null);

  // Store calculated values for export
  const [daysDifference, setDaysDifference] = useState<number>(30);
  const [isSameMonthYear, setIsSameMonthYear] = useState<boolean>(false);

  const [aggregatedAnalyticsData, setAggregatedAnalyticsData] =
    useState<IAggregatedAnalyticsData | null>(null);

  const [count1, setCount1] = useState<EventCounts>({
    type: "Calls",
    total: 0,
  });
  const [count2, setCount2] = useState<EventCounts>({
    type: "Directions",
    total: 0,
  });
  const [count3, setCount3] = useState<EventCounts>({
    type: "Website clicks",
    total: 0,
  });

  const INITIAL_GRAPH_DATA: IGraphDataModel = { data: [], labels: [] };

  const [websiteData, setWebsiteData] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [callData, setCallData] = useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [directionsData, setDirectionsData] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [messagingClicks, setMessagingClicks] =
    useState<IGraphDataModel>(INITIAL_GRAPH_DATA);
  const [bookings, setBookings] = useState<IGraphDataModel>(INITIAL_GRAPH_DATA);

  // State for location chips functionality
  const [locationsWithData, setLocationsWithData] = useState<string[]>([]);
  const [originalLocationData, setOriginalLocationData] = useState<any[]>([]);
  const [selectedLocationIds, setSelectedLocationIds] = useState<string[]>([]);
  const [locationDataMap, setLocationDataMap] = useState<Record<string, any>>(
    {}
  );

  useEffect(() => {
    getBusiness();
    getBusinessGroups();
    fetchLocations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Clear results when any input value changes after data has been retrieved
  const clearAnalyticsResults = () => {
    setAggregatedAnalyticsData(null);
    setWebsiteData(INITIAL_GRAPH_DATA);
    setCallData(INITIAL_GRAPH_DATA);
    setDirectionsData(INITIAL_GRAPH_DATA);
    setMessagingClicks(INITIAL_GRAPH_DATA);
    setBookings(INITIAL_GRAPH_DATA);
    setCount1({ type: "Calls", total: 0 });
    setCount2({ type: "Directions", total: 0 });
    setCount3({ type: "Website clicks", total: 0 });
    // Clear location chips data
    setLocationsWithData([]);
    setOriginalLocationData([]);
    setSelectedLocationIds([]);
    setLocationDataMap({});
  };

  const VALIDATION_DAYS: number = 15;

  // Function to filter and recalculate analytics based on selected location chips
  const recalculateAnalyticsForSelectedLocations = useCallback(
    (selectedIds: string[]) => {
      if (
        Object.keys(locationDataMap).length === 0 ||
        selectedIds.length === 0
      ) {
        // If no locations selected, show zero values
        setWebsiteData(INITIAL_GRAPH_DATA);
        setCallData(INITIAL_GRAPH_DATA);
        setDirectionsData(INITIAL_GRAPH_DATA);
        setMessagingClicks(INITIAL_GRAPH_DATA);
        setBookings(INITIAL_GRAPH_DATA);
        setCount1({ type: "Calls", total: 0 });
        setCount2({ type: "Directions", total: 0 });
        setCount3({ type: "Website clicks", total: 0 });
        return;
      }

      // Filter location data to only include selected locations
      const filteredLocationData = selectedIds
        .map((locationId) => locationDataMap[locationId])
        .filter((data) => data !== undefined);

      // Get the current date range info
      const daysDifference = selectedDateRange
        ? _applicationHelperService.getDaysDifference(
            selectedDateRange.from,
            selectedDateRange.to
          )
        : 15;
      const isSameMonthYear = selectedDateRange?.isSameMonthYear || false;

      // Recalculate metrics with filtered data
      const webSiteClicksData = aggregateMetricData(
        filteredLocationData,
        "WEBSITE_CLICKS",
        daysDifference,
        isSameMonthYear
      );
      setWebsiteData(webSiteClicksData);
      handleDataFromChild(webSiteClicksData, "Website clicks");

      const callData = aggregateMetricData(
        filteredLocationData,
        "CALL_CLICKS",
        daysDifference,
        isSameMonthYear
      );
      setCallData(callData);
      handleDataFromChild(callData, "Calls");

      const directionsData = aggregateMetricData(
        filteredLocationData,
        "BUSINESS_DIRECTION_REQUESTS",
        daysDifference,
        isSameMonthYear
      );
      setDirectionsData(directionsData);
      handleDataFromChild(directionsData, "Directions");

      setMessagingClicks(
        aggregateMetricData(
          filteredLocationData,
          "BUSINESS_CONVERSATIONS",
          daysDifference,
          isSameMonthYear
        )
      );

      setBookings(
        aggregateMetricData(
          filteredLocationData,
          "BUSINESS_FOOD_ORDERS",
          daysDifference,
          isSameMonthYear
        )
      );
    },
    [locationDataMap, selectedDateRange, _applicationHelperService]
  );

  // Handle location chip toggle
  const handleLocationToggle = (locationId: string, isSelected: boolean) => {
    // This is handled by the LocationChips component
    console.log(`Location ${locationId} toggled: ${isSelected}`);
  };

  // Handle location selection change
  const handleLocationSelectionChange = useCallback(
    (selectedIds: string[]) => {
      setSelectedLocationIds(selectedIds);
      recalculateAnalyticsForSelectedLocations(selectedIds);
    },
    [recalculateAnalyticsForSelectedLocations]
  );

  // Export all charts functionality
  const handleExportAllCharts = useCallback(async () => {
    if (!aggregatedAnalyticsData || !selectedDateRange) {
      setToastConfig(
        ToastSeverity.Warning,
        "No data available to export. Please select business, account, locations and date range first.",
        true
      );
      return;
    }

    if (selectedLocationIds.length === 0) {
      setToastConfig(
        ToastSeverity.Warning,
        "No locations selected. Please select at least one location.",
        true
      );
      return;
    }

    try {
      setLoading(true);

      // Prepare platform breakdown data
      const platformData: { label: string; value: number }[] = [];
      if (aggregatedAnalyticsData.multiDailyMetricTimeSeries[0]) {
        const series =
          aggregatedAnalyticsData.multiDailyMetricTimeSeries[0]
            .dailyMetricTimeSeries;
        const platformResult = series
          .filter((x) => x.dailyMetric.includes("BUSINESS_IMPRESSIONS"))
          .map((metric) => {
            const label = metric.dailyMetric
              .replace("BUSINESS_IMPRESSIONS_", "")
              .replace(/_/g, " ")
              .toLowerCase()
              .replace(/(^\w|\s\w)/g, (m) => m.toUpperCase());

            const value =
              metric.timeSeries.datedValues &&
              metric.timeSeries.datedValues.reduce((acc, curr) => {
                return acc + (curr.value ? parseInt(curr.value) : 0);
              }, 0);

            return { label, value };
          });
        platformData.push(...platformResult);
      }

      // Prepare all chart data for export
      const allChartsData: ChartExportData[] = [
        {
          chartTitle: "Platform Breakdown",
          data: {
            data: platformData.map((entry) => entry.value),
            labels: platformData.map((entry) => entry.label),
          },
          selectedLocationIds,
          availableLocations: locationList,
          locationDataMap,
          metricType: "BUSINESS_IMPRESSIONS",
          daysDifference,
          isSameMonthYear,
          dateRange: {
            from: selectedDateRange.from,
            to: selectedDateRange.to,
          },
        },
        {
          chartTitle: "Calls (Aggregated)",
          data: callData,
          selectedLocationIds,
          availableLocations: locationList,
          locationDataMap,
          metricType: "CALL_CLICKS",
          daysDifference,
          isSameMonthYear,
          dateRange: {
            from: selectedDateRange.from,
            to: selectedDateRange.to,
          },
        },
        {
          chartTitle: "Directions (Aggregated)",
          data: directionsData,
          selectedLocationIds,
          availableLocations: locationList,
          locationDataMap,
          metricType: "BUSINESS_DIRECTION_REQUESTS",
          daysDifference,
          isSameMonthYear,
          dateRange: {
            from: selectedDateRange.from,
            to: selectedDateRange.to,
          },
        },
        {
          chartTitle: "Website Clicks (Aggregated)",
          data: websiteData,
          selectedLocationIds,
          availableLocations: locationList,
          locationDataMap,
          metricType: "WEBSITE_CLICKS",
          daysDifference,
          isSameMonthYear,
          dateRange: {
            from: selectedDateRange.from,
            to: selectedDateRange.to,
          },
        },
        {
          chartTitle: "Messaging Clicks (Aggregated)",
          data: messagingClicks,
          selectedLocationIds,
          availableLocations: locationList,
          locationDataMap,
          metricType: "BUSINESS_CONVERSATIONS",
          daysDifference,
          isSameMonthYear,
          dateRange: {
            from: selectedDateRange.from,
            to: selectedDateRange.to,
          },
        },
        {
          chartTitle: "Bookings (Aggregated)",
          data: bookings,
          selectedLocationIds,
          availableLocations: locationList,
          locationDataMap,
          metricType: "BUSINESS_FOOD_ORDERS",
          daysDifference,
          isSameMonthYear,
          dateRange: {
            from: selectedDateRange.from,
            to: selectedDateRange.to,
          },
        },
      ];

      // Export all charts to a single Excel file
      ExcelExportService.exportMultipleChartsToExcel(
        allChartsData,
        "GMB_Analytics_Complete_Report"
      );

      setToastConfig(
        ToastSeverity.Success,
        "Analytics data exported successfully",
        true
      );
    } catch (error) {
      console.error("Failed to export analytics data:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to export analytics data",
        true
      );
    } finally {
      setLoading(false);
    }
  }, [
    aggregatedAnalyticsData,
    selectedDateRange,
    selectedLocationIds,
    locationList,
    locationDataMap,
    daysDifference,
    isSameMonthYear,
    callData,
    directionsData,
    websiteData,
    messagingClicks,
    bookings,
    setLoading,
    setToastConfig,
  ]);

  // MenuProps for multi-select dropdown
  const MenuProps = {
    PaperProps: {
      style: {
        maxHeight: 300,
        width: "auto", // Allow auto width to show full content
        minWidth: 300, // Minimum width for better readability
      },
    },
    getContentAnchorEl: null,
    anchorOrigin: {
      vertical: "bottom" as const,
      horizontal: "left" as const,
    },
    transformOrigin: {
      vertical: "top" as const,
      horizontal: "left" as const,
    },
  };

  // Handle form submission
  const handleFormSubmit = (values: IAnalyticsFilterModel) => {
    if (selectedDateRange && values.locationIds.length > 0) {
      fetchAggregatedAnalyticsData(
        selectedDateRange.from,
        selectedDateRange.to,
        selectedDateRange.isSameMonthYear,
        values.locationIds.filter((id) => id !== "select-all")
      );
    } else if (!selectedDateRange) {
      setToastConfig(ToastSeverity.Warning, "Please select a date range", true);
    }
  };

  // Validation schema
  const AnalyticsSchema = yup.object().shape({
    businessId: yup
      .number()
      .typeError("Business ID must be a number")
      .moreThan(0, "Business must be selected")
      .required("Business is required"),
    businessGroupId: yup
      .string()
      .nonNullable()
      .required("Account is required")
      .test("len", `Account is required`, (val) => val != "0"),
    locationIds: yup
      .array()
      .of(yup.string())
      .min(1, "At least one location must be selected")
      .required("Locations are required"),
  });

  // Function to fetch businesses
  const getBusiness = async () => {
    try {
      setLoading(true);
      const roles: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (roles.list.length > 0) {
        setBusinessList(roles.list);
      }
    } catch (error) {
      console.error("Failed to fetch businesses", error);
      setToastConfig(ToastSeverity.Error, "Failed to fetch businesses", true);
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch business groups
  const getBusinessGroups = async () => {
    try {
      setLoading(true);
      const businessGroups: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (businessGroups.data.length > 0) {
        setBusinessGroups(businessGroups.data);
      }
    } catch (error) {
      console.error("Failed to fetch business groups", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to fetch business groups",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch all locations for the user
  const fetchLocations = async () => {
    try {
      setLoading(true);
      const locationListResponse: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      console.log("Location List: ", locationListResponse.list);
      setLocationList(locationListResponse.list);
    } catch (error) {
      console.error("Failed to fetch locations", error);
      setToastConfig(ToastSeverity.Error, "Failed to fetch locations", true);
    } finally {
      setLoading(false);
    }
  };

  // Function to aggregate metrics from all locations
  const aggregateMetricData = (
    allLocationData: any[],
    metric: string,
    daysDifference: number,
    isSameMonthYear: boolean
  ) => {
    const aggregatedDaily: Record<string, number> = {};
    const aggregatedMonthly: Record<string, number> = {};

    // Process each location's data
    allLocationData.forEach((locationData) => {
      if (!locationData || !locationData.multiDailyMetricTimeSeries) return;

      // Handle the nested structure properly
      locationData.multiDailyMetricTimeSeries.forEach((multiMetric: any) => {
        if (!multiMetric || !multiMetric.dailyMetricTimeSeries) return;

        const metricSeries = multiMetric.dailyMetricTimeSeries.find(
          (series: any) => series && series.dailyMetric === metric
        );

        if (
          !metricSeries ||
          !metricSeries.timeSeries ||
          !metricSeries.timeSeries.datedValues
        )
          return;

        // Aggregate values for this location
        for (const entry of metricSeries.timeSeries.datedValues) {
          if (!entry.date) continue;

          const { year, month, day } = entry.date;
          const key =
            daysDifference < VALIDATION_DAYS || isSameMonthYear
              ? dayjs(`${year}-${month}-${day}`).format("YYYY-MMM-DD")
              : dayjs(`${year}-${month}-01`).format("MMM YYYY");

          const value = parseInt(entry.value ?? "0", 10);
          if (daysDifference < VALIDATION_DAYS || isSameMonthYear) {
            aggregatedDaily[key] = (aggregatedDaily[key] || 0) + value;
          } else {
            aggregatedMonthly[key] = (aggregatedMonthly[key] || 0) + value;
          }
        }
      });
    });

    const labels =
      daysDifference < VALIDATION_DAYS || isSameMonthYear
        ? Object.keys(aggregatedDaily).sort()
        : Object.keys(aggregatedMonthly);
    const data =
      daysDifference < VALIDATION_DAYS || isSameMonthYear
        ? labels.map((label) => aggregatedDaily[label] || 0)
        : labels.map((label) => aggregatedMonthly[label] || 0);

    return { data, labels };
  };

  // Function to fetch and aggregate analytics data from selected locations
  const fetchAggregatedAnalyticsData = async (
    from: string,
    to: string,
    isSameMonthYear: boolean,
    selectedLocationIds: string[]
  ) => {
    const daysDifference: number = _applicationHelperService.getDaysDifference(
      from,
      to
    );

    // Store these values for export
    setDaysDifference(daysDifference);
    setIsSameMonthYear(isSameMonthYear);

    try {
      setLoading(true);
      const requestObj: ILocationMetricsRequestModel = {
        startDate: from,
        endDate: to,
      };

      // Filter locations based on selected location IDs and remove duplicates
      const uniqueSelectedLocationIds = [...new Set(selectedLocationIds)];
      const selectedLocations = locationList.filter((location) =>
        uniqueSelectedLocationIds.includes(location.gmbLocationId)
      );

      if (selectedLocations.length === 0) {
        setToastConfig(
          ToastSeverity.Warning,
          "No valid locations selected",
          true
        );
        return;
      }

      // Use the new multi-location analytics endpoint for better performance
      console.log("Fetching analytics data using multi-location endpoint...");

      const response = await _locationMetricsService.getMultiLocationAnalytics({
        locationIds: uniqueSelectedLocationIds,
        startDate: from,
        endDate: to,
      });

      console.log("Multi-location analytics response:", response);

      if (!response.data) {
        throw new Error("No data received from analytics service");
      }

      const aggregatedData = response.data;

      // Check if we have any data
      if (
        !aggregatedData.multiDailyMetricTimeSeries ||
        aggregatedData.multiDailyMetricTimeSeries.length === 0 ||
        !aggregatedData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries ||
        aggregatedData.multiDailyMetricTimeSeries[0].dailyMetricTimeSeries
          .length === 0
      ) {
        console.log(
          "No analytics data found in database, locations may need sync"
        );
        setToastConfig(
          ToastSeverity.Warning,
          `No analytics data found for the selected date range. ${
            aggregatedData.locationsWithoutData?.length || 0
          } locations may need data synchronization.`,
          true
        );

        // Set empty data but still show the structure
        setAggregatedAnalyticsData({
          multiDailyMetricTimeSeries: [],
          totalLocations: selectedLocations.length,
          processedLocations: 0,
        });
        return;
      }

      // Create location data map for individual location filtering
      const dataMap: Record<string, any> = {};
      const locationsWithValidData: string[] =
        aggregatedData.locationsWithData || [];

      // For backward compatibility, create individual location data entries
      // This allows the existing location chips functionality to work
      locationsWithValidData.forEach((locationId) => {
        dataMap[locationId] = {
          multiDailyMetricTimeSeries: aggregatedData.multiDailyMetricTimeSeries,
        };
      });

      setLocationDataMap(dataMap);
      setLocationsWithData(locationsWithValidData);
      setSelectedLocationIds(locationsWithValidData); // Initially select all locations with data

      // Store the aggregated data for processing
      const validLocationData = [aggregatedData];

      console.log("Aggregated Analytics Data:", validLocationData);

      // Store original data and track which locations have data
      setOriginalLocationData(validLocationData);

      // The new endpoint already returns aggregated data, so we can use it directly
      const finalAggregatedData: IAggregatedAnalyticsData = {
        multiDailyMetricTimeSeries: aggregatedData.multiDailyMetricTimeSeries,
        totalLocations: aggregatedData.totalLocations,
        processedLocations: aggregatedData.processedLocations,
      };

      setAggregatedAnalyticsData(finalAggregatedData);

      // Debug logging
      console.log("Final aggregated data structure:", finalAggregatedData);
      if (finalAggregatedData.multiDailyMetricTimeSeries.length > 0) {
        console.log(
          "First element dailyMetricTimeSeries:",
          finalAggregatedData.multiDailyMetricTimeSeries[0]
            .dailyMetricTimeSeries
        );
      }

      // Aggregate different metrics using the new data structure
      const webSiteClicksData = aggregateMetricData(
        [aggregatedData],
        "WEBSITE_CLICKS",
        daysDifference,
        isSameMonthYear
      );
      setWebsiteData(webSiteClicksData);
      handleDataFromChild(webSiteClicksData, "Website clicks");

      const callData = aggregateMetricData(
        [aggregatedData],
        "CALL_CLICKS",
        daysDifference,
        isSameMonthYear
      );
      setCallData(callData);
      handleDataFromChild(callData, "Calls");

      const directionsData = aggregateMetricData(
        [aggregatedData],
        "BUSINESS_DIRECTION_REQUESTS",
        daysDifference,
        isSameMonthYear
      );
      setDirectionsData(directionsData);
      handleDataFromChild(directionsData, "Directions");

      setMessagingClicks(
        aggregateMetricData(
          [aggregatedData],
          "BUSINESS_CONVERSATIONS",
          daysDifference,
          isSameMonthYear
        )
      );

      setBookings(
        aggregateMetricData(
          [aggregatedData],
          "BUSINESS_FOOD_ORDERS",
          daysDifference,
          isSameMonthYear
        )
      );

      // Note: We no longer show the warning toast as we have location chips to show this information
    } catch (error: any) {
      console.error("Failed to fetch aggregated analytics data", error);
      setWebsiteData(INITIAL_GRAPH_DATA);
      setCallData(INITIAL_GRAPH_DATA);
      setDirectionsData(INITIAL_GRAPH_DATA);
      setMessagingClicks(INITIAL_GRAPH_DATA);
      setBookings(INITIAL_GRAPH_DATA);
      setCount1({ ...count1, total: 0 });
      setCount2({ ...count2, total: 0 });
      setCount3({ ...count3, total: 0 });
      setAggregatedAnalyticsData(null);

      if (error.response?.data?.error) {
        setToastConfig(ToastSeverity.Error, error.response.data.error, true);
      } else if (error.response?.data?.message) {
        setToastConfig(ToastSeverity.Error, error.response.data.message, true);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Failed to fetch analytics data",
          true
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDataFromChild = (data: any, type: string) => {
    console.log("Aggregated data from child:", data, type);
    const total =
      data &&
      data.data &&
      data.data.reduce((sum: number, val: number) => sum + val, 0);
    if (type === "Calls") {
      setCount1({
        ...count1,
        total: total,
      });
    } else if (type === "Directions") {
      setCount2({ ...count2, total: total });
    } else if (type === "Website clicks") {
      setCount3({ ...count3, total: total });
    }
  };

  return (
    <div>
      <Box sx={{background:"white"}}>
        <Box>
          <LeftMenuComponent>
            <Box
              sx={{
                pr: 1,
              }}
            >
              <Box>
                <Box>
                  <Box sx={{ marginBottom: "5px" }}>
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "flex-start",
                        mb: 1,
                      }}
                    >
                      <Box>
                        <h3 className="pageTitle">
                          Analytics - Selected Locations
                        </h3>
                        <Typography variant="subtitle2" className="subtitle2">
                          Hi, {userInfo && userInfo.name}. Select business,
                          account, and locations to view analytics data.
                        </Typography>
                        {aggregatedAnalyticsData && (
                          <Typography
                            variant="body2"
                            sx={{ mt: 1, color: "text.secondary" }}
                          >
                            Showing data from{" "}
                            {aggregatedAnalyticsData.processedLocations} out of{" "}
                            {aggregatedAnalyticsData.totalLocations} selected
                            locations
                          </Typography>
                        )}
                      </Box>
                      {aggregatedAnalyticsData && (
                        <Button
                          variant="contained"
                          color="primary"
                          startIcon={<FileDownloadIcon />}
                          onClick={handleExportAllCharts}
                          sx={{
                            minHeight: "50px",
                            borderRadius: "8px",
                            textTransform: "none",
                            fontWeight: "bold",
                            px: 3,
                            boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
                            "&:hover": {
                              boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
                              transform: "translateY(-1px)",
                            },
                            transition: "all 0.2s ease-in-out",
                          }}
                        >
                          Export All Charts
                        </Button>
                      )}
                    </Box>
                  </Box>

                  <Formik
                    initialValues={INITIAL_VALUES}
                    validationSchema={AnalyticsSchema}
                    onSubmit={handleFormSubmit}
                  >
                    {({
                      values,
                      errors,
                      touched,
                      handleChange,
                      handleBlur,
                      handleSubmit,
                      setFieldValue,
                      isValid,
                    }) => (
                      <form onSubmit={handleSubmit}>
                        <Grid container spacing={2}>
                          <Grid item xs={12} md={6} lg={6}>
                            <FormControl
                              className="commonSelect"
                              variant="outlined"
                              fullWidth
                              error={Boolean(
                                getIn(errors, "businessId") &&
                                  getIn(touched, "businessId")
                              )}
                            >
                              <InputLabel id="business-dropdown-label">
                                Business
                              </InputLabel>
                              <Select
                                fullWidth
                                id="businessId"
                                label="Business"
                                value={values.businessId.toString()}
                                onChange={(evt: SelectChangeEvent) => {
                                  // Clear results when business changes
                                  if (aggregatedAnalyticsData) {
                                    clearAnalyticsResults();
                                  }
                                  setFieldValue("businessGroupId", "0");
                                  setFieldValue("locationIds", []);
                                  setFieldValue(
                                    "businessId",
                                    +evt.target.value
                                  );
                                }}
                                onBlur={handleBlur}
                                sx={{
                                  backgroundColor: "var(--whiteColor)",
                                  borderRadius: "5px",
                                }}
                              >
                                <MenuItem value={0}>Select</MenuItem>
                                {businessList &&
                                  businessList.map((business: IBusiness) => (
                                    <MenuItem
                                      key={business.id}
                                      value={business.id.toString()}
                                    >
                                      {business.businessName}
                                    </MenuItem>
                                  ))}
                              </Select>
                              <FormHelperText>
                                {touched.businessId && errors.businessId
                                  ? errors.businessId
                                  : ""}
                              </FormHelperText>
                            </FormControl>
                          </Grid>

                          <Grid item xs={12} md={6} lg={6}>
                            <FormControl
                              className="commonSelect"
                              variant="outlined"
                              fullWidth
                              error={Boolean(
                                getIn(errors, "businessGroupId") &&
                                  getIn(touched, "businessGroupId")
                              )}
                            >
                              <InputLabel id="account-dropdown-label">
                                Account
                              </InputLabel>
                              <Select
                                fullWidth
                                id="businessGroupId"
                                label="Account"
                                value={values.businessGroupId.toString()}
                                onChange={(evt: SelectChangeEvent) => {
                                  // Clear results when account changes
                                  if (aggregatedAnalyticsData) {
                                    clearAnalyticsResults();
                                  }
                                  setFieldValue(
                                    "businessGroupId",
                                    evt.target.value
                                  );
                                  setFieldValue("locationIds", []);
                                }}
                                onBlur={handleBlur}
                                sx={{
                                  backgroundColor: "var(--whiteColor)",
                                  borderRadius: "5px",
                                }}
                              >
                                <MenuItem value={"0"}>Select</MenuItem>
                                {businessGroups
                                  .filter(
                                    (x: IBusinessGroup) =>
                                      x.businessId === values.businessId
                                  )
                                  .map((businessGroup: IBusinessGroup) => (
                                    <MenuItem
                                      key={businessGroup.accountId}
                                      value={businessGroup.accountId.toString()}
                                    >
                                      {businessGroup.accountName}
                                    </MenuItem>
                                  ))}
                              </Select>
                              <FormHelperText>
                                {touched.businessGroupId &&
                                errors.businessGroupId
                                  ? errors.businessGroupId
                                  : ""}
                              </FormHelperText>
                            </FormControl>
                          </Grid>

                          <Grid item xs={12} md={6} lg={6}>
                            <FormControl
                              className="commonSelect"
                              variant="outlined"
                              fullWidth
                              error={Boolean(
                                getIn(errors, "locationIds") &&
                                  getIn(touched, "locationIds")
                              )}
                              sx={OUTLINED_INPUT_STYLES}
                            >
                              <InputLabel id="locations-dropdown-label">
                                Locations
                              </InputLabel>
                              <Select
                                fullWidth
                                multiple
                                id="locationIds"
                                label="Locations"
                                value={values.locationIds}
                                onChange={() => {
                                  // Selection is now handled by individual MenuItem onClick handlers
                                  // This prevents the default Select behavior
                                }}
                                input={<OutlinedInput label="Locations" />}
                                MenuProps={MenuProps}
                                renderValue={(selected) => {
                                  const selectedArray = selected as string[];
                                  const isSelectAllChecked =
                                    selectedArray.includes("select-all");
                                  const availableLocationIds = locationList
                                    .filter(
                                      (x: ILocation) =>
                                        x.gmbAccountId ===
                                        values.businessGroupId
                                    )
                                    .map((location) => location.gmbLocationId);

                                  // Filter out "select-all" from the selected array for accurate counting
                                  const actualSelectedLocations =
                                    selectedArray.filter(
                                      (id) => id !== "select-all"
                                    );

                                  // Fallback for when no locations are selected
                                  if (actualSelectedLocations.length === 0) {
                                    return (
                                      <Box
                                        sx={{
                                          display: "flex",
                                          alignItems: "center",
                                          color: "text.secondary",
                                          fontStyle: "italic",
                                        }}
                                      >
                                        Select Locations
                                      </Box>
                                    );
                                  }

                                  if (
                                    isSelectAllChecked &&
                                    actualSelectedLocations.length ===
                                      availableLocationIds.length
                                  ) {
                                    // All locations are selected
                                    return (
                                      <Box
                                        sx={{
                                          display: "flex",
                                          alignItems: "center",
                                          width: "100%",
                                          justifyContent: "flex-start",
                                        }}
                                      >
                                        <Chip
                                          label={`All Locations (${availableLocationIds.length})`}
                                          size="small"
                                          color="primary"
                                          sx={{
                                            fontWeight: "bold",
                                          }}
                                        />
                                      </Box>
                                    );
                                  }

                                  return (
                                    <Box
                                      sx={{
                                        display: "flex",
                                        flexWrap: "nowrap", // Prevent wrapping to second line
                                        gap: 0.5,
                                        width: "100%",
                                        alignItems: "center",
                                        justifyContent: "flex-start",
                                        maxHeight: "56px",
                                        overflow: "hidden",
                                      }}
                                    >
                                      {actualSelectedLocations
                                        .slice(0, 2) // Show only first 2 items
                                        .map((value) => {
                                          const location = locationList.find(
                                            (loc) => loc.gmbLocationId === value
                                          );
                                          return (
                                            <Chip
                                              key={value}
                                              label={
                                                location?.gmbLocationName ||
                                                value
                                              }
                                              size="small"
                                              sx={{
                                                maxWidth: "140px", // Slightly smaller to fit better
                                                "& .MuiChip-label": {
                                                  overflow: "hidden",
                                                  textOverflow: "ellipsis",
                                                  whiteSpace: "nowrap",
                                                },
                                              }}
                                            />
                                          );
                                        })}
                                      {actualSelectedLocations.length > 2 && (
                                        <Chip
                                          label={`+${
                                            actualSelectedLocations.length - 2
                                          } more`}
                                          size="small"
                                          variant="outlined"
                                          color="primary"
                                          sx={{
                                            ml: 0.5,
                                            flexShrink: 0, // Prevent shrinking
                                          }}
                                        />
                                      )}
                                    </Box>
                                  );
                                }}
                              >
                                {/* Only show options if account is selected */}
                                {values.businessGroupId !== "0" ? (
                                  <>
                                    {/* Select All option */}
                                    <MenuItem
                                      value="select-all"
                                      sx={{
                                        borderBottom:
                                          "1px solid rgba(0, 0, 0, 0.12)",
                                        fontWeight: "bold",
                                        paddingY: 1,
                                        whiteSpace: "nowrap",
                                        overflow: "visible",
                                      }}
                                      onClick={(e) => {
                                        e.preventDefault();
                                        // Clear results when locations change
                                        if (aggregatedAnalyticsData) {
                                          clearAnalyticsResults();
                                        }
                                        const availableLocationIds =
                                          locationList
                                            .filter(
                                              (x: ILocation) =>
                                                x.gmbAccountId ===
                                                values.businessGroupId
                                            )
                                            .map(
                                              (location) =>
                                                location.gmbLocationId
                                            );

                                        if (
                                          values.locationIds.includes(
                                            "select-all"
                                          )
                                        ) {
                                          // Deselect all
                                          setFieldValue("locationIds", []);
                                        } else {
                                          // Select all
                                          setFieldValue("locationIds", [
                                            "select-all",
                                            ...availableLocationIds,
                                          ]);
                                        }
                                      }}
                                    >
                                      <Checkbox
                                        checked={values.locationIds.includes(
                                          "select-all"
                                        )}
                                        sx={{ mr: 1 }}
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          // Clear results when locations change
                                          if (aggregatedAnalyticsData) {
                                            clearAnalyticsResults();
                                          }
                                          const availableLocationIds =
                                            locationList
                                              .filter(
                                                (x: ILocation) =>
                                                  x.gmbAccountId ===
                                                  values.businessGroupId
                                              )
                                              .map(
                                                (location) =>
                                                  location.gmbLocationId
                                              );

                                          if (
                                            values.locationIds.includes(
                                              "select-all"
                                            )
                                          ) {
                                            // Deselect all
                                            setFieldValue("locationIds", []);
                                          } else {
                                            // Select all
                                            setFieldValue("locationIds", [
                                              "select-all",
                                              ...availableLocationIds,
                                            ]);
                                          }
                                        }}
                                      />
                                      <ListItemText
                                        primary="Select All Locations"
                                        sx={{ whiteSpace: "nowrap" }}
                                      />
                                    </MenuItem>

                                    {/* Individual locations */}
                                    {locationList
                                      .filter(
                                        (x: ILocation) =>
                                          x.gmbAccountId ===
                                          values.businessGroupId
                                      )
                                      .map((location: ILocation) => (
                                        <MenuItem
                                          key={location.gmbLocationId}
                                          value={location.gmbLocationId}
                                          sx={{
                                            whiteSpace: "nowrap",
                                            overflow: "visible",
                                          }}
                                          onClick={(e) => {
                                            e.preventDefault();
                                            // Clear results when locations change
                                            if (aggregatedAnalyticsData) {
                                              clearAnalyticsResults();
                                            }
                                            const availableLocationIds =
                                              locationList
                                                .filter(
                                                  (x: ILocation) =>
                                                    x.gmbAccountId ===
                                                    values.businessGroupId
                                                )
                                                .map(
                                                  (loc) => loc.gmbLocationId
                                                );

                                            let newLocationIds = [
                                              ...values.locationIds,
                                            ];

                                            if (
                                              newLocationIds.includes(
                                                location.gmbLocationId
                                              )
                                            ) {
                                              // Remove this location
                                              newLocationIds =
                                                newLocationIds.filter(
                                                  (id) =>
                                                    id !==
                                                      location.gmbLocationId &&
                                                    id !== "select-all"
                                                );
                                            } else {
                                              // Add this location
                                              newLocationIds =
                                                newLocationIds.filter(
                                                  (id) => id !== "select-all"
                                                );
                                              newLocationIds.push(
                                                location.gmbLocationId
                                              );

                                              // Check if all locations are now selected
                                              if (
                                                newLocationIds.length ===
                                                availableLocationIds.length
                                              ) {
                                                newLocationIds.push(
                                                  "select-all"
                                                );
                                              }
                                            }

                                            setFieldValue(
                                              "locationIds",
                                              newLocationIds
                                            );
                                          }}
                                        >
                                          <Checkbox
                                            checked={values.locationIds.includes(
                                              location.gmbLocationId
                                            )}
                                            sx={{ mr: 1 }}
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              // Clear results when locations change
                                              if (aggregatedAnalyticsData) {
                                                clearAnalyticsResults();
                                              }
                                              const availableLocationIds =
                                                locationList
                                                  .filter(
                                                    (x: ILocation) =>
                                                      x.gmbAccountId ===
                                                      values.businessGroupId
                                                  )
                                                  .map(
                                                    (loc) => loc.gmbLocationId
                                                  );

                                              let newLocationIds = [
                                                ...values.locationIds,
                                              ];

                                              if (
                                                newLocationIds.includes(
                                                  location.gmbLocationId
                                                )
                                              ) {
                                                // Remove this location
                                                newLocationIds =
                                                  newLocationIds.filter(
                                                    (id) =>
                                                      id !==
                                                        location.gmbLocationId &&
                                                      id !== "select-all"
                                                  );
                                              } else {
                                                // Add this location
                                                newLocationIds =
                                                  newLocationIds.filter(
                                                    (id) => id !== "select-all"
                                                  );
                                                newLocationIds.push(
                                                  location.gmbLocationId
                                                );

                                                // Check if all locations are now selected
                                                if (
                                                  newLocationIds.length ===
                                                  availableLocationIds.length
                                                ) {
                                                  newLocationIds.push(
                                                    "select-all"
                                                  );
                                                }
                                              }

                                              setFieldValue(
                                                "locationIds",
                                                newLocationIds
                                              );
                                            }}
                                          />
                                          <ListItemText
                                            primary={location.gmbLocationName}
                                            sx={{ whiteSpace: "nowrap" }}
                                          />
                                        </MenuItem>
                                      ))}
                                  </>
                                ) : (
                                  <MenuItem disabled>
                                    Please select an account first
                                  </MenuItem>
                                )}
                              </Select>
                              <FormHelperText>
                                {touched.locationIds && errors.locationIds
                                  ? errors.locationIds
                                  : ""}
                              </FormHelperText>
                            </FormControl>
                          </Grid>

                          <Grid item xs={12} md={6} lg={3}>
                            <DateFilter
                              onDateChange={(range: any) => {
                                // Clear results when date range changes
                                if (aggregatedAnalyticsData) {
                                  clearAnalyticsResults();
                                }
                                setSelectedDateRange(range);
                              }}
                            />
                          </Grid>

                          <Grid item xs={12} md={6} lg={3}>
                            <FormControl
                              className="commonSelect"
                              variant="filled"
                              sx={{ height: "100%"}}
                            >
                              <Button
                                className="commonShapeBtn"
                                variant="contained"
                                disabled={
                                  !isValid ||
                                  !selectedDateRange ||
                                  values.locationIds.length === 0
                                }
                                sx={{
                                  minHeight: "55px", // Set the desired height
                                  mt:1,

                                }}
                                type="submit"
                                startIcon={<AnalyticsIcon />}
                              >
                                <span className="responsiveHide">
                                  View Analytics
                                </span>
                              </Button>
                            </FormControl>
                          </Grid>
                        </Grid>
                      </form>
                    )}
                  </Formik>

                  {/* Show location chips if data is available */}
                  {locationsWithData.length > 0 && (
                    <LocationChips
                      availableLocations={locationList}
                      locationsWithData={locationsWithData}
                      onLocationToggle={handleLocationToggle}
                      onSelectionChange={handleLocationSelectionChange}
                    />
                  )}

                  {/* Only show analytics data if it's available */}
                  {aggregatedAnalyticsData ? (
                    <>
                      <Box>
                        <Grid
                          container
                          spacing={2}
                          className="commonCardBottomSpacing"
                        >
                          <Grid item xs={12} md={4} lg={4}>
                            <Box className="commonCard dashboardTopIconCard">
                              <Box className="dashboardTopIcon">
                                <img
                                  alt="MyLocoBiz - Logo"
                                  className="innerImage1"
                                  src={picture2Image}
                                />
                              </Box>
                              <Box className="dashboardTopInfo">
                                <Typography className="dashboardTopCount">
                                  {count1.total}
                                </Typography>
                                <Typography className="dashboardTopTitle">
                                  {count1.type} (All Locations)
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>
                          <Grid item xs={12} md={4} lg={4}>
                            <Box className="commonCard dashboardTopIconCard">
                              <Box className="dashboardTopIcon">
                                <img
                                  alt="MyLocoBiz - Logo"
                                  className="innerImage2"
                                  src={picture1Image}
                                />
                              </Box>
                              <Box className="dashboardTopInfo">
                                <Typography className="dashboardTopCount">
                                  {count2.total}
                                </Typography>
                                <Typography className="dashboardTopTitle">
                                  {count2.type} (All Locations)
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>
                          <Grid item xs={12} md={4} lg={4}>
                            <Box className="commonCard dashboardTopIconCard">
                              <Box className="dashboardTopIcon">
                                <img
                                  alt="MyLocoBiz - Logo"
                                  className="innerImage3"
                                  src={picture3Image}
                                />
                              </Box>
                              <Box className="dashboardTopInfo">
                                <Typography className="dashboardTopCount">
                                  {count3.total}
                                </Typography>
                                <Typography className="dashboardTopTitle">
                                  {count3.type} (All Locations)
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>
                        </Grid>

                        <Grid
                          container
                          spacing={2}
                          className="commonCardBottomSpacing"
                        >
                          {/* BusinessProfileInteractionsChart temporarily replaced with summary card */}

                          {/* Show a placeholder message for now */}
                          <Grid item xs={12} md={12}>
                            <Box
                              className="commonCard"
                              sx={{ p: 3, textAlign: "center" }}
                            >
                              <Typography variant="h6" gutterBottom>
                                Business Profile Interactions (Aggregated)
                              </Typography>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                Aggregated interactions data from{" "}
                                {locationList.length} location(s)
                              </Typography>
                              <Typography
                                variant="h4"
                                sx={{ mt: 2, color: "primary.main" }}
                              >
                                {(
                                  count1.total +
                                  count2.total +
                                  count3.total
                                ).toLocaleString()}
                              </Typography>
                              <Typography
                                variant="caption"
                                color="text.secondary"
                              >
                                Total interactions (Calls + Directions + Website
                                Clicks)
                              </Typography>
                            </Box>
                          </Grid>

                          <Grid item xs={12} md={6} lg={6}>
                            <Box className="commonCard height100 pieChartDiv">
                              <Box
                                sx={{
                                  p: 2,
                                  backgroundColor: "#fff",
                                  borderRadius: 2,
                                }}
                              >
                                <PlatformBreakdownChart
                                  data={{
                                    multiDailyMetricTimeSeries:
                                      aggregatedAnalyticsData?.multiDailyMetricTimeSeries ||
                                      [],
                                  }}
                                  showExport={true}
                                  selectedLocationIds={selectedLocationIds}
                                  availableLocations={locationList}
                                  locationDataMap={locationDataMap}
                                  daysDifference={daysDifference}
                                  isSameMonthYear={isSameMonthYear}
                                  dateRange={
                                    selectedDateRange
                                      ? {
                                          from: selectedDateRange.from,
                                          to: selectedDateRange.to,
                                        }
                                      : undefined
                                  }
                                />
                              </Box>
                            </Box>
                          </Grid>
                          <Grid item xs={12} md={6} lg={6}>
                            <RevenueChartDashboard
                              data1={callData.data}
                              data2={directionsData.data}
                              labels={directionsData.labels}
                              title1="Call Data (All Locations)"
                              title2="Directions Data (All Locations)"
                              graphTitle="Directions Vs Calls - Aggregated"
                              showExport={true}
                              selectedLocationIds={selectedLocationIds}
                              availableLocations={locationList}
                              locationDataMap={locationDataMap}
                              metricType="CALL_CLICKS"
                              daysDifference={daysDifference}
                              isSameMonthYear={isSameMonthYear}
                              dateRange={
                                selectedDateRange
                                  ? {
                                      from: selectedDateRange.from,
                                      to: selectedDateRange.to,
                                    }
                                  : undefined
                              }
                            />
                          </Grid>
                        </Grid>
                      </Box>
                      <Box>
                        <Grid
                          container
                          spacing={2}
                          className="commonCardBottomSpacing"
                        >
                          <Grid item xs={12} md={6}>
                            <WebsiteClicksChart
                              {...callData}
                              title="Call clicks made from all Business Profiles"
                              graphTitle="Calls (Aggregated)"
                              colorCode={0}
                              showExport={true}
                              selectedLocationIds={selectedLocationIds}
                              availableLocations={locationList}
                              locationDataMap={locationDataMap}
                              metricType="CALL_CLICKS"
                              daysDifference={daysDifference}
                              isSameMonthYear={isSameMonthYear}
                              dateRange={
                                selectedDateRange
                                  ? {
                                      from: selectedDateRange.from,
                                      to: selectedDateRange.to,
                                    }
                                  : undefined
                              }
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <WebsiteClicksChart
                              {...messagingClicks}
                              title="Messaging clicks made from all Business Profiles"
                              graphTitle="Messaging clicks (Aggregated)"
                              colorCode={1}
                              showExport={true}
                              selectedLocationIds={selectedLocationIds}
                              availableLocations={locationList}
                              locationDataMap={locationDataMap}
                              metricType="BUSINESS_CONVERSATIONS"
                              daysDifference={daysDifference}
                              isSameMonthYear={isSameMonthYear}
                              dateRange={
                                selectedDateRange
                                  ? {
                                      from: selectedDateRange.from,
                                      to: selectedDateRange.to,
                                    }
                                  : undefined
                              }
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <WebsiteClicksChart
                              {...bookings}
                              title="Bookings made from all Business Profiles"
                              graphTitle="Bookings (Aggregated)"
                              colorCode={0}
                              showExport={true}
                              selectedLocationIds={selectedLocationIds}
                              availableLocations={locationList}
                              locationDataMap={locationDataMap}
                              metricType="BUSINESS_FOOD_ORDERS"
                              daysDifference={daysDifference}
                              isSameMonthYear={isSameMonthYear}
                              dateRange={
                                selectedDateRange
                                  ? {
                                      from: selectedDateRange.from,
                                      to: selectedDateRange.to,
                                    }
                                  : undefined
                              }
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <WebsiteClicksChart
                              {...directionsData}
                              title="Direction requests made from all Business Profiles"
                              graphTitle="Directions (Aggregated)"
                              colorCode={1}
                              showExport={true}
                              selectedLocationIds={selectedLocationIds}
                              availableLocations={locationList}
                              locationDataMap={locationDataMap}
                              metricType="BUSINESS_DIRECTION_REQUESTS"
                              daysDifference={daysDifference}
                              isSameMonthYear={isSameMonthYear}
                              dateRange={
                                selectedDateRange
                                  ? {
                                      from: selectedDateRange.from,
                                      to: selectedDateRange.to,
                                    }
                                  : undefined
                              }
                            />
                          </Grid>

                          <Grid item xs={12} md={6}>
                            <WebsiteClicksChart
                              {...websiteData}
                              title="Website clicks made from all Business Profiles"
                              graphTitle="Website clicks (Aggregated)"
                              colorCode={0}
                              showExport={true}
                              selectedLocationIds={selectedLocationIds}
                              availableLocations={locationList}
                              locationDataMap={locationDataMap}
                              metricType="WEBSITE_CLICKS"
                              daysDifference={daysDifference}
                              isSameMonthYear={isSameMonthYear}
                              dateRange={
                                selectedDateRange
                                  ? {
                                      from: selectedDateRange.from,
                                      to: selectedDateRange.to,
                                    }
                                  : undefined
                              }
                            />
                          </Grid>

                          {/* <Grid item xs={12} md={6}>
                            <Box className="commonCard height100 pieChartDiv">
                              <HomeChartCard />
                            </Box>
                          </Grid> */}
                        </Grid>
                      </Box>
                    </>
                  ) : (
                    <Box sx={{ textAlign: "center", py: 4 }}>
                      <Typography variant="h6" color="text.secondary">
                        Please select business, account, locations, and date
                        range to view analytics data
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>
    </div>
  );
};

export default GMBReports;
