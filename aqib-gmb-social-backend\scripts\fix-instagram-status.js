const mysql = require("mysql2/promise");
require("dotenv").config();

/**
 * <PERSON><PERSON><PERSON> to fix Instagram OAuth tokens with missing status_id
 * This script updates all Instagram OAuth tokens to have status_id = 1
 * if they currently have NULL or 0 status_id values
 */
async function fixInstagramTokenStatus() {
  let connection;

  try {
    console.log("Starting Instagram token status fix...");

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.APP_DB_HOST,
      user: process.env.APP_DB_USER,
      password: process.env.APP_DB_PASSWORD,
      database: process.env.APP_DB_NAME,
    });

    console.log("Connected to database successfully");

    // First, check current status
    const [currentTokens] = await connection.execute(
      "SELECT id, user_id, instagram_user_id, status_id FROM instagram_oauth_tokens"
    );

    console.log("Current Instagram OAuth tokens:", {
      totalTokens: currentTokens.length,
      tokensWithNullStatus: currentTokens.filter((t) => t.status_id === null)
        .length,
      tokensWithZeroStatus: currentTokens.filter((t) => t.status_id === 0)
        .length,
      tokensWithActiveStatus: currentTokens.filter((t) => t.status_id === 1)
        .length,
    });

    // Update tokens with NULL or 0 status_id to 1
    const [updateResult] = await connection.execute(
      "UPDATE instagram_oauth_tokens SET status_id = 1 WHERE status_id IS NULL OR status_id = 0"
    );

    console.log("Instagram token status update completed:", {
      affectedRows: updateResult.affectedRows,
    });

    // Verify the fix
    const [updatedTokens] = await connection.execute(
      "SELECT id, user_id, instagram_user_id, status_id FROM instagram_oauth_tokens"
    );

    console.log("Updated Instagram OAuth tokens:", {
      totalTokens: updatedTokens.length,
      tokensWithNullStatus: updatedTokens.filter((t) => t.status_id === null)
        .length,
      tokensWithZeroStatus: updatedTokens.filter((t) => t.status_id === 0)
        .length,
      tokensWithActiveStatus: updatedTokens.filter((t) => t.status_id === 1)
        .length,
    });

    console.log("Instagram token status fix completed successfully!");
  } catch (error) {
    console.error("Error fixing Instagram token status:", error.message);
    throw error;
  } finally {
    // Close the database connection
    if (connection) {
      await connection.end();
      console.log("Database connection closed");
    }
  }
}

// Run the script if called directly
if (require.main === module) {
  fixInstagramTokenStatus()
    .then(() => {
      console.log("✅ Instagram token status fix completed successfully!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Instagram token status fix failed:", error.message);
      process.exit(1);
    });
}

module.exports = {
  fixInstagramTokenStatus,
};
