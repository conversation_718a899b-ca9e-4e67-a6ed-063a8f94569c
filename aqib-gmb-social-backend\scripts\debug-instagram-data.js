const mysql = require("mysql2/promise");
require("dotenv").config({ path: ".env.development" });

/**
 * <PERSON>ript to debug Instagram data in the database
 */
async function debugInstagramData() {
  let connection;

  try {
    console.log("Starting Instagram data debug...");

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.APP_DB_HOST,
      user: process.env.APP_DB_USER,
      password: process.env.APP_DB_PASSWORD,
      database: process.env.APP_DB_NAME,
    });

    console.log("Connected to database successfully");

    // Check ALL Instagram OAuth tokens
    const [allOauthTokens] = await connection.execute(
      "SELECT id, user_id, instagram_user_id, instagram_user_name, instagram_user_email, status_id, created_at FROM instagram_oauth_tokens"
    );

    console.log("ALL Instagram OAuth tokens:", {
      totalTokens: allOauthTokens.length,
      tokens: allOauthTokens.map((t) => ({
        id: t.id,
        user_id: t.user_id,
        instagram_user_id: t.instagram_user_id,
        instagram_user_name: t.instagram_user_name,
        instagram_user_email: t.instagram_user_email,
        status_id: t.status_id,
        created_at: t.created_at,
      })),
    });

    // Check Instagram OAuth tokens for user 132 (the actual authenticated user)
    const [oauthTokens132] = await connection.execute(
      "SELECT id, user_id, instagram_user_id, instagram_user_name, instagram_user_email, status_id, created_at FROM instagram_oauth_tokens WHERE user_id = 132"
    );

    console.log("Instagram OAuth tokens for user 132:", {
      totalTokens: oauthTokens132.length,
      tokens: oauthTokens132.map((t) => ({
        id: t.id,
        instagram_user_id: t.instagram_user_id,
        instagram_user_name: t.instagram_user_name,
        instagram_user_email: t.instagram_user_email,
        status_id: t.status_id,
        created_at: t.created_at,
      })),
    });

    // Check Instagram accounts for user 132
    const [instagramAccounts132] = await connection.execute(
      `SELECT ia.*, iot.instagram_user_name, iot.status_id as oauth_status_id
       FROM instagram_accounts ia
       LEFT JOIN instagram_oauth_tokens iot ON ia.instagram_oauth_token_id = iot.id
       WHERE iot.user_id = 132`
    );

    console.log("Instagram accounts for user 132:", {
      totalAccounts: instagramAccounts132.length,
      accounts: instagramAccounts132.map((a) => ({
        id: a.id,
        account_id: a.account_id,
        account_name: a.account_name,
        account_username: a.account_username,
        account_type: a.account_type,
        is_active: a.is_active,
        oauth_status_id: a.oauth_status_id,
        created_at: a.created_at,
      })),
    });

    // Check Instagram OAuth tokens for user 1
    const [oauthTokens] = await connection.execute(
      "SELECT id, user_id, instagram_user_id, instagram_user_name, instagram_user_email, status_id, created_at FROM instagram_oauth_tokens WHERE user_id = 1"
    );

    console.log("Instagram OAuth tokens for user 1:", {
      totalTokens: oauthTokens.length,
      tokens: oauthTokens.map((t) => ({
        id: t.id,
        instagram_user_id: t.instagram_user_id,
        instagram_user_name: t.instagram_user_name,
        instagram_user_email: t.instagram_user_email,
        status_id: t.status_id,
        created_at: t.created_at,
      })),
    });

    // Check Instagram accounts for user 1
    const [instagramAccounts] = await connection.execute(
      `SELECT ia.*, iot.instagram_user_name, iot.status_id as oauth_status_id
       FROM instagram_accounts ia
       LEFT JOIN instagram_oauth_tokens iot ON ia.instagram_oauth_token_id = iot.id
       WHERE iot.user_id = 1`
    );

    console.log("Instagram accounts for user 1:", {
      totalAccounts: instagramAccounts.length,
      accounts: instagramAccounts.map((a) => ({
        id: a.id,
        account_id: a.account_id,
        account_name: a.account_name,
        account_username: a.account_username,
        account_type: a.account_type,
        is_active: a.is_active,
        oauth_status_id: a.oauth_status_id,
        created_at: a.created_at,
      })),
    });

    // Check if there are any Instagram accounts at all
    const [allInstagramAccounts] = await connection.execute(
      "SELECT COUNT(*) as total FROM instagram_accounts"
    );

    console.log(
      "Total Instagram accounts in database:",
      allInstagramAccounts[0].total
    );

    // Check if there are any OAuth tokens at all
    const [allOAuthTokens] = await connection.execute(
      "SELECT COUNT(*) as total FROM instagram_oauth_tokens"
    );

    console.log(
      "Total Instagram OAuth tokens in database:",
      allOAuthTokens[0].total
    );
  } catch (error) {
    console.error("Debug failed:", error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run the debug
debugInstagramData();
