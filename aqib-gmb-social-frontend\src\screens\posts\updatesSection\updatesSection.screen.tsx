import React from "react";
import {
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Box,
  Grid,
  CardMedia,
} from "@mui/material";
import { useNavigate } from "react-router-dom";
import { TOPIC_TYPES } from "../../../constants/application.constant";
import { useSelector } from "react-redux";

import createNewPostImage from "../../../assets/common/create-new-post.jpg";
import offersDealsImage from "../../../assets/common/offers-deals.jpg";
import eventUpdatesImage from "../../../assets/common/event-updates.jpg";

// Individual Update Card Component
const UpdateCard = (props: {
  title: string;
  description: string;
  buttonText: string;
  buttonColor: string;
  bgColor: string;
  imagePath: string;
  navigationType: string;
  isDisabled: boolean;
}) => {
  const navigate = useNavigate();
  const {
    title,
    description,
    buttonText,
    buttonColor,
    bgColor,
    imagePath,
    navigationType,
    isDisabled,
  } = props;

  return (
    <Card
      sx={{
        backgroundColor: bgColor,
        borderRadius: 2,
        boxShadow: 2,
        display: "flex",
        flexDirection: "column",
        height: "100%",
        transition: "0.3s",
        "&:hover": {
          transform: "translateY(-4px)",
          boxShadow: 4,
        },
      }}
    >
      <CardMedia
        component="img"
        height="180"
        image={imagePath}
        alt={title}
        sx={{ objectFit: "cover" }}
      />
      <CardContent sx={{ display: "flex", flexDirection: "column", flexGrow: 1 }}>
        <Typography variant="h6" fontWeight="bold" gutterBottom>
          {title}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ flexGrow: 1 }}>
          {description}
        </Typography>
        <Button
          className="updatesShapeBtn"
          variant="contained"
          fullWidth
          sx={{ mt: 2 }}
          color={
            buttonColor === "primary"
              ? "primary"
              : buttonColor === "success"
              ? "success"
              : "secondary"
          }
          onClick={() => navigate(`/post-management/create-social-post`)}
          disabled={!isDisabled}
        >
          {buttonText}
        </Button>
      </CardContent>
    </Card>
  );
};

// Main Updates Section
const UpdatesSection = () => {
  const { rbAccess } = useSelector((state: any) => state.authReducer);

  const updatesData = [
    {
      id: 1,
      title: "Post Updates",
      description: "Update your customers with the latest information about your Brand.",
      buttonText: "Create New Post",
      buttonColor: "secondary",
      bgColor: "#E8F0FE",
      imagePath: createNewPostImage,
      navigationType: TOPIC_TYPES.Event,
      isDisabled: Boolean(rbAccess && rbAccess.PostsCreate),
    },
    {
      id: 2,
      title: "Offers & Deals Updates",
      description: "Add Offers & Deals to generate more customer interest & impressions",
      buttonText: "Create New Offer",
      buttonColor: "success",
      bgColor: "#E0F7FA",
      imagePath: offersDealsImage,
      navigationType: TOPIC_TYPES.Offer,
      isDisabled: Boolean(rbAccess && rbAccess.PostsCreate),
    },
    {
      id: 3,
      title: "Event Updates",
      description: "Tell customers about upcoming Events & Occasions at your Location",
      buttonText: "Update Event Updates",
      buttonColor: "primary",
      bgColor: "#FCE4EC",
      imagePath: eventUpdatesImage,
      navigationType: TOPIC_TYPES.Event,
      isDisabled: Boolean(rbAccess && rbAccess.PostsCreate),
    },
  ];

  return (
    <Box sx={{ px: { xs: 2, sm: 3, md: 4 }, py: 4 }}>
      <Grid container spacing={{ xs: 3, md: 4 }} justifyContent="center">
        {updatesData.map((update) => (
          <Grid
            item
            key={update.id}
            xs={12}
            md={6}
            xl={4}
            sx={{
              display: "flex",
            }}
          >
            <UpdateCard {...update} />
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default UpdatesSection;
