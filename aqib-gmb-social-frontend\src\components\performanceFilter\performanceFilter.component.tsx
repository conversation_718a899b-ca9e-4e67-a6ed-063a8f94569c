import React, { useEffect, useState } from "react";
import {
  Box,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Typography,
  Button,
} from "@mui/material";

import PerformanceDateFilter from "../performanceDateFilter/performanceDateFilter.component";
import BusinessService from "../../services/business/business.service";

import FileDownloadIcon from "@mui/icons-material/FileDownload";
import { useDispatch, useSelector } from "react-redux";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../interfaces/response/IBusinessListResponseModel";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../interfaces/response/IBusinessGroupsResponseModel";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../interfaces/response/ILocationsListResponseModel";
import { SELECT_DROPDOWN_STYLES } from "../../constants/styles.constant";
import FilterListIcon from "@mui/icons-material/FilterList";

interface IReviewsPerformanceFilterProps {
  onFilterChange: (filters: IReviewsPerformanceFilterData) => void;
  onExport?: () => void;
  showExport?: boolean;
}

export interface IReviewsPerformanceFilterData {
  businessId: string;
  accountId: string;
  locationId: string;
  fromDate: string;
  toDate: string;
}

const ReviewsPerformanceFilter: React.FC<IReviewsPerformanceFilterProps> = ({
  onFilterChange,
  onExport,
  showExport = false,
}) => {
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const [accountList, setAccountList] = useState<IBusinessGroup[]>([]);
  const [locationList, setLocationList] = useState<ILocation[]>([]);

  const [selectedBusiness, setSelectedBusiness] = useState<string>("0");
  const [selectedAccount, setSelectedAccount] = useState<string>("0");
  const [selectedLocation, setSelectedLocation] = useState<string>("0");
  const [fromDate, setFromDate] = useState<string>("");
  const [toDate, setToDate] = useState<string>("");
  const [datesValid, setDatesValid] = useState<boolean>(false);

  const dispatch = useDispatch();
  const { userInfo } = useSelector((state: any) => state.authReducer);
  const _businessService = new BusinessService(dispatch);

  useEffect(() => {
    fetchBusinessList();
  }, []);

  useEffect(() => {
    if (selectedBusiness !== "0") {
      fetchAccountList();
    } else {
      setAccountList([]);
      setSelectedAccount("0");
      setLocationList([]);
      setSelectedLocation("0");
    }
  }, [selectedBusiness]);

  useEffect(() => {
    if (selectedAccount !== "0") {
      fetchLocationList();
    } else {
      setLocationList([]);
      setSelectedLocation("0");
    }
  }, [selectedAccount]);

  // Validate dates whenever they change
  useEffect(() => {
    validateBothDates();
  }, [fromDate, toDate]);

  // Validation function
  const isFormValid = () => {
    return (
      selectedBusiness !== "0" &&
      selectedAccount !== "0" &&
      selectedLocation !== "0" &&
      fromDate !== "" &&
      toDate !== "" &&
      datesValid === true
    );
  };

  // Handle Apply button click
  const handleApplyFilters = () => {
    if (!isFormValid()) {
      return;
    }

    const filters: IReviewsPerformanceFilterData = {
      businessId: selectedBusiness,
      accountId: selectedAccount,
      locationId: selectedLocation,
      fromDate: fromDate,
      toDate: toDate,
    };
    onFilterChange(filters);
  };

  const fetchBusinessList = async () => {
    try {
      const response: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (response.list.length > 0) {
        setBusinessList(response.list);
      }
    } catch (error) {
      console.error("Error fetching business list:", error);
    }
  };

  const fetchAccountList = async () => {
    try {
      const response: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (response.data.length > 0) {
        // Filter accounts by selected business
        const filteredAccounts = response.data.filter(
          (account) => account.businessId?.toString() === selectedBusiness
        );
        setAccountList(filteredAccounts);
      }
    } catch (error) {
      console.error("Error fetching account list:", error);
    }
  };

  const fetchLocationList = async () => {
    try {
      const response: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      if (response.list.length > 0) {
        // Filter locations by selected account
        const filteredLocations = response.list.filter(
          (location) => location.gmbAccountId === selectedAccount
        );
        setLocationList(filteredLocations);
      }
    } catch (error) {
      console.error("Error fetching location list:", error);
    }
  };

  const handleBusinessChange = (event: SelectChangeEvent) => {
    setSelectedBusiness(event.target.value);
    setSelectedAccount("0");
    setSelectedLocation("0");
  };

  const handleAccountChange = (event: SelectChangeEvent) => {
    setSelectedAccount(event.target.value);
    setSelectedLocation("0");
  };

  const handleLocationChange = (event: SelectChangeEvent) => {
    setSelectedLocation(event.target.value);
  };

  // Helper function to validate both dates
  const validateBothDates = (newFromDate?: string, newToDate?: string) => {
    const currentFromDate = newFromDate || fromDate;
    const currentToDate = newToDate || toDate;

    if (currentFromDate && currentToDate) {
      const from = new Date(currentFromDate);
      const to = new Date(currentToDate);
      const today = new Date();

      // Check if dates are valid and not in future
      const isValid = from <= today && to <= today && from < to;
      setDatesValid(isValid);
    } else {
      setDatesValid(false);
    }
  };

  return (
    <Box sx={{ mb: 3 }}>
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 3,
        }}
      >
        <Typography variant="h6" fontWeight="bold"></Typography>
        {showExport && onExport && (
          <Button
            variant="contained"
            startIcon={<FileDownloadIcon />}
            onClick={onExport}
            sx={{
              minHeight: "40px", // Set the desired height
              textTransform: "none",
              borderRadius: "5px",
            }}
          >
            Export Report
          </Button>
        )}
      </Box>

      <Grid container spacing={3}>
        {/* First Row: Business, Account, Location */}
        <Grid item xs={12} md={4}>
          <FormControl fullWidth variant="outlined" required>
            <InputLabel>Business</InputLabel>
            <Select
              value={selectedBusiness}
              onChange={handleBusinessChange}
              label="Business"
              error={selectedBusiness === "0"}
              sx={SELECT_DROPDOWN_STYLES}
            >
              <MenuItem value="0" disabled>
                Select Business
              </MenuItem>
              {businessList.map((business) => (
                <MenuItem key={business.id} value={business.id.toString()}>
                  {business.businessName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={4}>
          <FormControl fullWidth variant="outlined" required>
            <InputLabel>Account</InputLabel>
            <Select
              value={selectedAccount}
              onChange={handleAccountChange}
              label="Account"
              disabled={selectedBusiness === "0"}
              error={selectedAccount === "0"}
              sx={SELECT_DROPDOWN_STYLES}
            >
              <MenuItem value="0" disabled>
                Select Account
              </MenuItem>
              {accountList.map((account) => (
                <MenuItem key={account.accountId} value={account.accountId}>
                  {account.accountName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={4}>
          <FormControl fullWidth variant="outlined" required>
            <InputLabel>Location</InputLabel>
            <Select
              value={selectedLocation}
              onChange={handleLocationChange}
              label="Location"
              disabled={selectedAccount === "0"}
              error={selectedLocation === "0"}
              sx={SELECT_DROPDOWN_STYLES}
            >
              <MenuItem value="0" disabled>
                Select Location
              </MenuItem>
              {locationList.map((location) => (
                <MenuItem
                  key={location.gmbLocationId}
                  value={location.gmbLocationId}
                >
                  {location.gmbLocationName}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Second Row: From Date, To Date, Analyze Button */}
        <Grid item xs={12} md={4}>
          <PerformanceDateFilter
            onDateChange={(range) => {
              if (range.fromDate) {
                setFromDate(range.fromDate);
                validateBothDates(range.fromDate, toDate);
              }
            }}
            dateType="from"
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <PerformanceDateFilter
            onDateChange={(range) => {
              if (range.toDate) {
                setToDate(range.toDate);
                validateBothDates(fromDate, range.toDate);
              }
            }}
            dateType="to"
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <Button
            className="commonShapeBtn"
            variant="contained"
            onClick={handleApplyFilters}
            disabled={!isFormValid()}
            sx={{
              minHeight: "56px",
              fontSize: "1rem",
              p:2,
              mt:1
            }}
            startIcon={<FilterListIcon />}
          >
            Analyze Performance
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ReviewsPerformanceFilter;
