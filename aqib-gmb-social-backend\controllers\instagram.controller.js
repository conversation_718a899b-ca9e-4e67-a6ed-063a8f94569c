const logger = require("../utils/logger");
const Instagram = require("../models/instagram.models");
const InstagramService = require("../services/instagram.service");

// Create Instagram service instance
const instagramService = new InstagramService();

/**
 * Welcome endpoint for Instagram API
 */
const welcome = async (req, res) => {
  try {
    logger.logControllerAction("instagram", "welcome", req.requestId);

    res.status(200).json({
      success: true,
      message: "Welcome to Instagram API",
      version: "1.0.0",
      endpoints: {
        authenticate: "POST /v1/instagram/authenticate",
        callback: "GET /v1/instagram/callback",
        callbackValidation: "POST /v1/instagram/callback-validation",
        getAccounts: "GET /v1/instagram/accounts/:userId",
        createPost: "POST /v1/instagram/posts/:userId",
      },
    });
  } catch (error) {
    logger.error("Error in Instagram welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Initiate Instagram authentication (POST - returns auth URL)
 */
const authenticate = async (req, res) => {
  try {
    logger.logControllerAction("instagram", "authenticate", req.requestId);

    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    logger.info("Instagram authentication initiated", {
      requestId: req.requestId,
      userId,
    });

    // Validate Instagram configuration
    if (!instagramService.validateConfig()) {
      return res.status(500).json({
        success: false,
        message: "Instagram configuration is incomplete",
      });
    }

    const authUrl = instagramService.generateAuthUrl(userId);

    logger.info("Instagram authentication URL generated", {
      requestId: req.requestId,
      userId,
      hasAuthUrl: !!authUrl,
    });

    res.status(200).json({
      success: true,
      authUrl: authUrl,
      message: "Instagram authentication URL generated successfully",
    });
  } catch (error) {
    logger.error("Error in Instagram authenticate", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Handle Instagram OAuth callback (GET - redirects to frontend)
 */
const callback = async (req, res) => {
  try {
    logger.logControllerAction("instagram", "callback", req.requestId);

    const { code, state, error, error_description } = req.query;

    // Handle OAuth errors
    if (error) {
      logger.error("Instagram OAuth error", {
        requestId: req.requestId,
        error,
        error_description,
      });

      const frontendUrl = process.env.FRONTEND_URL;
      return res.redirect(
        `${frontendUrl}/business-management/instagram/callback?error=${encodeURIComponent(
          error
        )}&error_description=${encodeURIComponent(error_description || "")}`
      );
    }

    if (!code || !state) {
      logger.error("Missing code or state in Instagram callback", {
        requestId: req.requestId,
        hasCode: !!code,
        hasState: !!state,
      });

      const frontendUrl = process.env.FRONTEND_URL;
      return res.redirect(
        `${frontendUrl}/business-management/instagram/callback?error=missing_parameters`
      );
    }

    // Redirect to frontend callback page with code and state
    const frontendUrl = process.env.FRONTEND_URL;
    res.redirect(
      `${frontendUrl}/business-management/instagram/callback?code=${encodeURIComponent(
        code
      )}&state=${encodeURIComponent(state)}`
    );
  } catch (error) {
    logger.error("Error in Instagram callback", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });

    const frontendUrl = process.env.FRONTEND_URL;
    res.redirect(
      `${frontendUrl}/business-management/instagram/callback?error=server_error`
    );
  }
};

/**
 * Handle Instagram OAuth callback validation (POST - processes the callback)
 */
const callbackValidation = async (req, res) => {
  try {
    logger.logControllerAction(
      "instagram",
      "callbackValidation",
      req.requestId
    );

    const { code, state } = req.body;

    if (!code || !state) {
      return res.status(400).json({
        success: false,
        message: "Authorization code and state are required",
      });
    }

    // Decode state to get user ID
    let userId;
    try {
      logger.info("Decoding state parameter", {
        requestId: req.requestId,
        state: state,
        stateLength: state.length,
      });

      const decodedState = JSON.parse(Buffer.from(state, "base64").toString());
      userId = decodedState.userId;

      logger.info("State decoded successfully", {
        requestId: req.requestId,
        decodedState,
        userId,
      });
    } catch (stateError) {
      logger.error("Invalid state parameter", {
        requestId: req.requestId,
        state,
        stateLength: state.length,
        error: stateError.message,
        stack: stateError.stack,
      });
      return res.status(400).json({
        success: false,
        message: "Invalid state parameter",
        debug: {
          state: state,
          error: stateError.message,
        },
      });
    }

    logger.info("Instagram callback validation started", {
      requestId: req.requestId,
      userId,
    });

    // Exchange code for access token
    const tokenData = await instagramService.exchangeCodeForToken(code);

    // Get Instagram user information
    const instagramUserInfo = await instagramService.getUserInfo(
      tokenData.access_token
    );

    logger.info("Instagram user info retrieved", {
      requestId: req.requestId,
      userId: instagramUserInfo.id,
      userName: instagramUserInfo.name,
      hasEmail: !!instagramUserInfo.email,
    });

    // Get user pages (for Instagram Business accounts)
    const pages = await instagramService.getUserPages(tokenData.access_token);

    logger.info("Instagram pages retrieved", {
      requestId: req.requestId,
      pagesCount: pages.length,
      pageIds: pages.map((p) => ({
        id: p.id,
        name: p.name,
        hasInstagram: !!p.instagram_business_account_id,
      })),
    });

    // Save OAuth tokens with Instagram user info
    const tokenResult = await Instagram.saveOAuthTokens({
      userId,
      instagramUserId: instagramUserInfo.id,
      instagramUserName: instagramUserInfo.name,
      instagramUserEmail: instagramUserInfo.email,
      instagramUserPicture: instagramUserInfo.picture?.data?.url,
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token || null,
      expiresAt: tokenData.expires_in
        ? new Date(Date.now() + tokenData.expires_in * 1000)
        : null,
    });

    // Get the inserted token ID for foreign key reference
    const instagramOAuthTokenId = tokenResult.result.insertId;

    // Save accounts with reference to the oauth token
    const accountsData = pages.map((page) => ({
      instagramOAuthTokenId,
      accountId: page.id,
      accountName: page.name,
      accountUsername: page.username || null,
      accountPictureUrl: page.picture?.data?.url,
      accountType: page.category || "business",
    }));

    if (accountsData.length > 0) {
      await Instagram.saveAccounts(accountsData);
    }

    logger.info("Instagram authentication completed successfully", {
      requestId: req.requestId,
      userId,
      instagramUserId: instagramUserInfo.id,
      instagramUserEmail: instagramUserInfo.email,
      accountsCount: pages.length,
    });

    res.status(200).json({
      success: true,
      message: "Instagram authentication completed successfully",
      data: {
        user: {
          id: instagramUserInfo.id,
          name: instagramUserInfo.name,
          email: instagramUserInfo.email,
          picture: instagramUserInfo.picture?.data?.url,
        },
        accountsCount: pages.length,
      },
    });
  } catch (error) {
    logger.error("Error in Instagram callback validation", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });

    let errorMessage = "Authentication failed";
    if (error.response?.data?.error?.message) {
      errorMessage = error.response.data.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: error.message,
      instagramError: error.response?.data?.error,
    });
  }
};

/**
 * Get Instagram accounts for user
 */
const getInstagramAccounts = async (req, res) => {
  try {
    logger.logControllerAction(
      "instagram",
      "getInstagramAccounts",
      req.requestId
    );

    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    logger.info("Getting Instagram accounts", {
      requestId: req.requestId,
      userId,
    });

    const result = await Instagram.getInstagramAccounts(parseInt(userId));

    if (result.success) {
      res.status(200).json({
        success: true,
        message: "Instagram accounts retrieved successfully",
        data: result.accounts,
      });
    } else {
      res.status(404).json({
        success: false,
        message: "No Instagram accounts found",
      });
    }
  } catch (error) {
    logger.error("Error getting Instagram accounts", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Get Instagram accounts/pages for user
 */
const getAccounts = async (req, res) => {
  try {
    logger.logControllerAction("instagram", "getAccounts", req.requestId);

    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    logger.info("Getting Instagram accounts", {
      requestId: req.requestId,
      userId,
    });

    const result = await Instagram.getAccounts(parseInt(userId));

    if (result.success) {
      res.status(200).json({
        success: true,
        message: "Instagram accounts retrieved successfully",
        data: { accounts: result.accounts },
      });
    } else {
      res.status(404).json({
        success: false,
        message: "No Instagram accounts found",
      });
    }
  } catch (error) {
    logger.error("Error getting Instagram accounts", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Create Instagram post
 */
const createPost = async (req, res) => {
  try {
    logger.logControllerAction("instagram", "createPost", req.requestId);

    const { userId } = req.params;
    const {
      accountId,
      caption,
      mediaUrl,
      mediaType,
      published,
      scheduledPublishTime,
    } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    if (!accountId || !caption || !mediaUrl) {
      return res.status(400).json({
        success: false,
        message: "Account ID, caption, and media URL are required",
      });
    }

    // Get account access token
    const accountsResult = await Instagram.getAccounts(parseInt(userId));
    const account = accountsResult.accounts.find(
      (a) => a.account_id === accountId
    );

    if (!account) {
      return res.status(404).json({
        success: false,
        message: "Instagram account not found",
      });
    }

    // Create Instagram post using the existing service method
    const instagramPostResponse = await instagramService.createInstagramPost(
      account.access_token || account.page_access_token,
      accountId,
      mediaUrl,
      caption,
      mediaType || "image"
    );

    // Generate Instagram URL
    const instagramUrl = instagramService.generatePostUrl(
      accountId,
      instagramPostResponse.id
    );

    // Save post to database
    const saveResult = await Instagram.savePost({
      userId: parseInt(userId),
      accountId: accountId,
      instagramPostId: instagramPostResponse.id,
      postContent: req.body,
      postResponse: instagramPostResponse,
      caption: caption,
      mediaUrl: mediaUrl,
      mediaType: mediaType || "image",
      published: published !== false,
      scheduledPublishTime: scheduledPublishTime || null,
      status: published !== false ? "published" : "scheduled",
      instagramUrl: instagramUrl,
    });

    if (saveResult.success) {
      logger.info("Instagram post created successfully", {
        requestId: req.requestId,
        userId,
        accountId,
        postId: instagramPostResponse.id,
        dbPostId: saveResult.postId,
      });

      res.status(200).json({
        success: true,
        message: "Instagram post created successfully",
        data: {
          id: saveResult.postId,
          instagramPostId: instagramPostResponse.id,
          accountId: accountId,
          caption: caption,
          mediaUrl: mediaUrl,
          instagramUrl: instagramUrl,
          status: published !== false ? "published" : "scheduled",
          createdTime: new Date().toISOString(),
        },
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Failed to save post to database",
      });
    }
  } catch (error) {
    logger.error("Error creating Instagram post", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });

    let errorMessage = "Failed to create Instagram post";
    if (error.response?.data?.error?.message) {
      errorMessage = error.response.data.error.message;
    } else if (error.message) {
      errorMessage = error.message;
    }

    res.status(500).json({
      success: false,
      message: errorMessage,
      error: error.message,
      instagramError: error.response?.data?.error,
    });
  }
};

/**
 * Get Instagram posts for user
 */
const getPosts = async (req, res) => {
  try {
    logger.logControllerAction("instagram", "getPosts", req.requestId);

    const { userId } = req.params;
    const { accountId, page = 1, limit = 10 } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    logger.info("Getting Instagram posts", {
      requestId: req.requestId,
      userId,
      accountId,
      page,
      limit,
    });

    const result = await Instagram.getPosts(
      parseInt(userId),
      accountId,
      parseInt(page),
      parseInt(limit)
    );

    if (result.success) {
      res.status(200).json({
        success: true,
        message: "Instagram posts retrieved successfully",
        data: result.posts,
        pagination: result.pagination,
      });
    } else {
      res.status(404).json({
        success: false,
        message: "No Instagram posts found",
      });
    }
  } catch (error) {
    logger.error("Error getting Instagram posts", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Debug endpoint to check Instagram account data
 */
const debugAccounts = async (req, res) => {
  try {
    logger.logControllerAction("instagram", "debugAccounts", req.requestId);

    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    // Get accounts using the model method
    const result = await Instagram.getAccounts(parseInt(userId));

    // Also get raw OAuth tokens for debugging
    const pool = require("../config/db");
    const rawTokens = await pool.query(
      "SELECT id, user_id, instagram_user_id, instagram_user_name, status_id, created_at FROM instagram_oauth_tokens WHERE user_id = ?",
      [parseInt(userId)]
    );

    // Get raw accounts for debugging
    const rawAccounts = await pool.query(
      "SELECT ia.*, iot.status_id as oauth_status_id FROM instagram_accounts ia LEFT JOIN instagram_oauth_tokens iot ON ia.instagram_oauth_token_id = iot.id WHERE iot.user_id = ?",
      [parseInt(userId)]
    );

    res.status(200).json({
      success: true,
      message: "Instagram accounts debug info",
      data: {
        accounts: result.accounts,
        accountsCount: result.accounts?.length || 0,
        accountIds:
          result.accounts?.map((acc) => ({
            account_id: acc.account_id,
            account_name: acc.account_name,
            account_username: acc.account_username,
            hasAccessToken: !!(acc.access_token || acc.page_access_token),
          })) || [],
        debug: {
          rawTokensCount: rawTokens.length,
          rawTokens: rawTokens.map((token) => ({
            id: token.id,
            user_id: token.user_id,
            instagram_user_id: token.instagram_user_id,
            instagram_user_name: token.instagram_user_name,
            status_id: token.status_id,
            created_at: token.created_at,
          })),
          rawAccountsCount: rawAccounts.length,
          rawAccounts: rawAccounts.map((acc) => ({
            id: acc.id,
            instagram_oauth_token_id: acc.instagram_oauth_token_id,
            account_id: acc.account_id,
            account_name: acc.account_name,
            is_active: acc.is_active,
            oauth_status_id: acc.oauth_status_id,
          })),
        },
      },
    });
  } catch (error) {
    logger.error("Error debugging Instagram accounts", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

/**
 * Debug endpoint to check Facebook pages and Instagram connections
 */
const debugPages = async (req, res) => {
  try {
    logger.logControllerAction("instagram", "debugPages", req.requestId);

    const { userId } = req.params;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: "User ID is required",
      });
    }

    // Get Instagram OAuth token for the user
    const pool = require("../config/db");
    const tokens = await pool.query(
      "SELECT access_token FROM instagram_oauth_tokens WHERE user_id = ? AND status_id = 1 ORDER BY created_at DESC LIMIT 1",
      [parseInt(userId)]
    );

    if (tokens.length === 0) {
      return res.status(404).json({
        success: false,
        message: "No Instagram OAuth token found for user",
      });
    }

    const accessToken = tokens[0].access_token;
    const instagramService = new InstagramService();

    // Get Facebook pages
    const pages = await instagramService.getUserPages(accessToken);

    res.status(200).json({
      success: true,
      message: "Facebook pages debug info",
      data: {
        totalPages: pages.length,
        pages: pages.map((page) => ({
          id: page.id,
          name: page.name,
          category: page.category,
          hasAccessToken: !!page.access_token,
          instagram_business_account_id: page.instagram_business_account_id,
          username: page.username,
        })),
      },
    });
  } catch (error) {
    logger.error("Error debugging Facebook pages", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
    });
  }
};

module.exports = {
  welcome,
  authenticate,
  callback,
  callbackValidation,
  getInstagramAccounts,
  getAccounts,
  createPost,
  getPosts,
  debugAccounts,
  debugPages,
};
