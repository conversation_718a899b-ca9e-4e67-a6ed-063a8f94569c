import React, { useState, useEffect, useRef, useMemo } from "react";
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Chip,
  Stack,
  Card,
  CardContent,
  Tooltip,
  IconButton,
  Switch,
  FormControlLabel,
} from "@mui/material";
import {
  LocationOn as LocationOnIcon,
  GridOn as GridOnIcon,
  ZoomIn as ZoomInIcon,
  TrendingUp as TrendingUpIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
} from "@mui/icons-material";
import {
  LocalFalconGridPoint,
  LocalFalconRankingResult,
  LocalFalconScanResult,
} from "../../services/localFalcon/localFalcon.service";
import {
  loadGoogleMapsAPI,
  isGoogleMapsAPIAvailable,
  isVisualizationLibraryAvailable,
} from "../../utils/googleMaps.utils";

interface LocalFalconMapProps {
  center: { lat: number; lng: number } | null;
  gridPoints: LocalFalconGridPoint[];
  rankingResults: LocalFalconRankingResult[];
  scanResult: LocalFalconScanResult | null;
  loading: boolean;
  showHeatMap: boolean;
  onShowHeatMapChange: (show: boolean) => void;
}

const LocalFalconMap: React.FC<LocalFalconMapProps> = ({
  center,
  gridPoints,
  rankingResults,
  scanResult,
  loading,
  showHeatMap,
  onShowHeatMapChange,
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapError, setMapError] = useState<string | null>(null);
  const [mapInstance, setMapInstance] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [heatmapLayer, setHeatmapLayer] =
    useState<google.maps.visualization.HeatmapLayer | null>(null);
  const [isGoogleMapsLoaded, setIsGoogleMapsLoaded] = useState(false);

  // Process raw Local Falcon API response to extract grid data
  const processRawScanData = (scanResult: LocalFalconScanResult | null) => {
    if (!scanResult?.rawResponse?.data?.results)
      return { processedGridPoints: [], processedRankings: [] };

    const rawResults = scanResult.rawResponse.data.results;
    const targetPlaceId = scanResult.rawResponse.parameters.place_id;
    const gridSize = parseInt(scanResult.rawResponse.parameters.grid_size) || 5;

    const processedGridPoints: LocalFalconGridPoint[] = [];
    const processedRankings: LocalFalconRankingResult[] = [];

    rawResults.forEach((result: any, index: number) => {
      // Create grid point
      const gridPoint: LocalFalconGridPoint = {
        lat: result.lat,
        lng: result.lng,
        index: index,
        gridPosition: {
          row: Math.floor(index / gridSize),
          col: index % gridSize,
        },
      };
      processedGridPoints.push(gridPoint);

      // Create ranking result if business was found
      if (result.found && result.rank) {
        const targetBusiness = result.results?.find(
          (r: any) => r.place_id === targetPlaceId
        );
        if (targetBusiness) {
          const rankingResult: LocalFalconRankingResult = {
            position: result.rank,
            business: {
              name: targetBusiness.business,
              placeId: targetBusiness.place_id,
              address: targetBusiness.address,
              rating: targetBusiness.rating,
              totalRatings: targetBusiness.reviews,
              lat: targetBusiness.lat,
              lng: targetBusiness.lng,
              isServiceAreaBusiness: false,
            },
            distance: 0, // Calculate if needed
            gridPoint: gridPoint,
            searchResults:
              result.results?.map((r: any) => ({
                name: r.business,
                placeId: r.place_id,
                address: r.address,
                rating: r.rating,
                totalRatings: r.reviews,
                lat: r.lat,
                lng: r.lng,
                isServiceAreaBusiness: false,
              })) || [],
          };
          processedRankings.push(rankingResult);
        }
      }
    });

    return { processedGridPoints, processedRankings };
  };

  // Get processed data from scan result (memoized to prevent infinite re-renders)
  const { effectiveGridPoints, effectiveRankingResults } = useMemo(() => {
    const { processedGridPoints, processedRankings } =
      processRawScanData(scanResult);

    // Use processed data if available, otherwise fall back to props
    const effectiveGridPoints =
      processedGridPoints.length > 0 ? processedGridPoints : gridPoints;
    const effectiveRankingResults =
      processedRankings.length > 0 ? processedRankings : rankingResults;

    return { effectiveGridPoints, effectiveRankingResults };
  }, [scanResult, gridPoints, rankingResults]);

  // Load Google Maps API on component mount
  useEffect(() => {
    const initializeGoogleMaps = async () => {
      try {
        // Check API key first
        const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
        console.log("Google Maps API Key:", apiKey ? "Present" : "Missing");

        if (!apiKey || apiKey === "YOUR_GOOGLE_MAPS_API_KEY") {
          throw new Error(
            "Google Maps API key not configured. Please set VITE_GOOGLE_MAPS_API_KEY in your environment variables."
          );
        }

        // Check if already loaded
        if (isGoogleMapsAPIAvailable()) {
          setIsGoogleMapsLoaded(true);
          return;
        }

        await loadGoogleMapsAPI();

        // Wait a bit for all libraries to be available
        let retries = 0;
        const maxRetries = 10;

        while (retries < maxRetries) {
          if (isGoogleMapsAPIAvailable()) {
            setIsGoogleMapsLoaded(true);
            return;
          }

          // Wait 100ms before checking again
          await new Promise((resolve) => setTimeout(resolve, 100));
          retries++;
        }

        // If we get here, the API didn't load properly
        throw new Error("Google Maps API did not load within expected time");
      } catch (error) {
        console.error("Failed to load Google Maps API:", error);
        setMapError(
          error instanceof Error
            ? error.message
            : "Failed to load Google Maps API. Please check your API key configuration and internet connection."
        );
      }
    };

    initializeGoogleMaps();
  }, []);

  // Initialize Google Maps
  useEffect(() => {
    if (
      !mapRef.current ||
      !center ||
      !isGoogleMapsLoaded ||
      !isGoogleMapsAPIAvailable()
    )
      return;

    try {
      const map = new google.maps.Map(mapRef.current, {
        center: center,
        zoom: 13,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        styles: [
          {
            featureType: "poi",
            elementType: "labels",
            stylers: [{ visibility: "off" }],
          },
        ],
        mapTypeControl: true,
        mapTypeControlOptions: {
          style: google.maps.MapTypeControlStyle.HORIZONTAL_BAR,
          position: google.maps.ControlPosition.TOP_CENTER,
        },
        zoomControl: true,
        zoomControlOptions: {
          position: google.maps.ControlPosition.RIGHT_CENTER,
        },
        scaleControl: true,
        streetViewControl: true,
        streetViewControlOptions: {
          position: google.maps.ControlPosition.RIGHT_TOP,
        },
        fullscreenControl: true,
      });

      setMapInstance(map);
      setMapError(null);
    } catch (error) {
      console.error("Error initializing map:", error);
      setMapError(
        "Failed to initialize map. Please check your internet connection."
      );
    }
  }, [center, isGoogleMapsLoaded]);

  // Create ranking color based on position
  const getRankingColor = (position: number): string => {
    if (position === 1) return "#4CAF50"; // Green for #1
    if (position <= 3) return "#8BC34A"; // Light green for top 3
    if (position <= 5) return "#FFC107"; // Yellow for top 5
    if (position <= 10) return "#FF9800"; // Orange for top 10
    if (position <= 20) return "#FF5722"; // Red-orange for top 20
    return "#F44336"; // Red for not found or low ranking
  };

  // Create marker with ranking number
  const createRankingMarker = (position: number, color: string) => {
    if (position > 0) {
      // Create a custom marker with ranking number
      return {
        path: google.maps.SymbolPath.CIRCLE,
        scale: 12,
        fillColor: color,
        fillOpacity: 0.9,
        strokeColor: "#ffffff",
        strokeWeight: 2,
        labelOrigin: new google.maps.Point(0, 0),
      };
    } else {
      // Create a marker for "not found"
      return {
        path: google.maps.SymbolPath.CIRCLE,
        scale: 8,
        fillColor: color,
        fillOpacity: 0.7,
        strokeColor: "#ffffff",
        strokeWeight: 2,
      };
    }
  };

  // Create grid point markers
  useEffect(() => {
    if (!mapInstance || !effectiveGridPoints.length) return;

    // Clear existing markers inline to avoid dependency issues
    markers.forEach((marker) => marker.setMap(null));

    const newMarkers: google.maps.Marker[] = [];

    effectiveGridPoints.forEach((point, index) => {
      const rankingResult = effectiveRankingResults.find(
        (r) => r.gridPoint.lat === point.lat && r.gridPoint.lng === point.lng
      );

      const position = rankingResult?.position || 0;
      const color = getRankingColor(position);

      // Create custom marker icon
      const markerIcon = createRankingMarker(position, color);

      const marker = new google.maps.Marker({
        position: { lat: point.lat, lng: point.lng },
        map: mapInstance,
        icon: markerIcon,
        title: `Grid Point ${index + 1}${
          position > 0 ? ` - Rank #${position}` : " - Not Found"
        }`,
        label:
          position > 0
            ? {
                text: position.toString(),
                color: "#ffffff",
                fontSize: "12px",
                fontWeight: "bold",
              }
            : undefined,
      });

      // Create enhanced info window with more details
      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div style="padding: 12px; min-width: 280px; font-family: Arial, sans-serif;">
            <h4 style="margin: 0 0 12px 0; color: #333; border-bottom: 2px solid ${color}; padding-bottom: 4px;">
              Grid Point ${index + 1}
            </h4>
            <div style="margin-bottom: 8px;">
              <strong>Grid Position:</strong> Row ${
                point.gridPosition?.row + 1 || "N/A"
              }, Col ${point.gridPosition?.col + 1 || "N/A"}
            </div>
            <div style="margin-bottom: 8px;">
              <strong>Coordinates:</strong> ${point.lat.toFixed(
                6
              )}, ${point.lng.toFixed(6)}
            </div>
            ${
              position > 0
                ? `<div style="margin-bottom: 8px; padding: 8px; background-color: ${color}20; border-left: 4px solid ${color}; border-radius: 4px;">
                <strong style="color: ${color};">Ranking: #${position}</strong>
                ${
                  rankingResult?.distance
                    ? `<br><small>Distance: ${rankingResult.distance.toFixed(
                        2
                      )} km</small>`
                    : ""
                }
              </div>`
                : `<div style="margin-bottom: 8px; padding: 8px; background-color: #f4433620; border-left: 4px solid #f44336; border-radius: 4px;">
                <strong style="color: #f44336;">Status: Not Found</strong>
              </div>`
            }
            ${
              rankingResult?.business?.name
                ? `<div style="margin-top: 8px; font-size: 12px; color: #666;">
                <strong>Business:</strong> ${rankingResult.business.name}
              </div>`
                : ""
            }
          </div>
        `,
      });

      marker.addListener("click", () => {
        infoWindow.open(mapInstance, marker);
      });

      newMarkers.push(marker);
    });

    setMarkers(newMarkers);
  }, [mapInstance, effectiveGridPoints, effectiveRankingResults]);

  // Create heatmap visualization
  useEffect(() => {
    if (!mapInstance || !showHeatMap || !effectiveRankingResults.length) {
      // Clear heatmap inline to avoid dependency issues
      if (heatmapLayer) {
        heatmapLayer.setMap(null);
        setHeatmapLayer(null);
      }
      return;
    }

    // Check if visualization library is available
    if (!isVisualizationLibraryAvailable()) {
      console.warn(
        "Google Maps Visualization library not available, skipping heatmap"
      );
      return;
    }

    try {
      // Prepare heatmap data
      const heatmapData = effectiveRankingResults
        .filter((result) => result.position > 0)
        .map((result) => ({
          location: new google.maps.LatLng(
            result.gridPoint.lat,
            result.gridPoint.lng
          ),
          weight: Math.max(1, 21 - result.position), // Higher weight for better rankings
        }));

      if (heatmapData.length === 0) {
        // Clear heatmap inline
        if (heatmapLayer) {
          heatmapLayer.setMap(null);
          setHeatmapLayer(null);
        }
        return;
      }

      const heatmap = new google.maps.visualization.HeatmapLayer({
        data: heatmapData,
        map: mapInstance,
        radius: 50,
        opacity: 0.6,
        gradient: [
          "rgba(0, 255, 255, 0)",
          "rgba(0, 255, 255, 1)",
          "rgba(0, 191, 255, 1)",
          "rgba(0, 127, 255, 1)",
          "rgba(0, 63, 255, 1)",
          "rgba(0, 0, 255, 1)",
          "rgba(0, 0, 223, 1)",
          "rgba(0, 0, 191, 1)",
          "rgba(0, 0, 159, 1)",
          "rgba(0, 0, 127, 1)",
          "rgba(63, 0, 91, 1)",
          "rgba(127, 0, 63, 1)",
          "rgba(191, 0, 31, 1)",
          "rgba(255, 0, 0, 1)",
        ],
      });

      setHeatmapLayer(heatmap);
    } catch (error) {
      console.error("Error creating heatmap:", error);
      // Fallback: disable heatmap if there's an error
      if (heatmapLayer) {
        heatmapLayer.setMap(null);
        setHeatmapLayer(null);
      }
    }
  }, [mapInstance, showHeatMap, effectiveRankingResults]);

  // Add center marker for business location
  useEffect(() => {
    if (!mapInstance || !center) return;

    const centerMarker = new google.maps.Marker({
      position: center,
      map: mapInstance,
      icon: {
        path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
        scale: 6,
        fillColor: "#2196F3",
        fillOpacity: 1,
        strokeColor: "#ffffff",
        strokeWeight: 2,
      },
      title: "Search Center",
    });

    return () => {
      centerMarker.setMap(null);
    };
  }, [mapInstance, center]);

  if (mapError) {
    return (
      <Alert severity="error" sx={{ height: "100%" }}>
        <Typography variant="h6">Map Error</Typography>
        <Typography>{mapError}</Typography>
      </Alert>
    );
  }

  return (
    <Box sx={{ height: "100%", position: "relative" }}>
      {/* Map Controls */}
      <Box sx={{ position: "absolute", top: 16, right: 16, zIndex: 1000, mt:12 }}>
        <Card sx={{ p: 1 }}>
          <Stack direction="column" spacing={1}>
            <FormControlLabel
              control={
                <Switch
                  checked={showHeatMap}
                  onChange={(e) => onShowHeatMapChange(e.target.checked)}
                  size="small"
                  disabled={!isVisualizationLibraryAvailable()}
                />
              }
              label="Heat Map"
              sx={{ m: 0 }}
            />

            {scanResult && (
              <Stack spacing={1}>
                <Chip
                  size="small"
                  icon={<TrendingUpIcon />}
                  label={`Avg Rank: ${scanResult.averagePosition.toFixed(1)}`}
                  color="primary"
                  variant="outlined"
                />
                <Chip
                  size="small"
                  icon={<VisibilityIcon />}
                  label={`Visibility: ${scanResult.visibilityPercentage.toFixed(
                    1
                  )}%`}
                  color="secondary"
                  variant="outlined"
                />
                <Chip
                  size="small"
                  icon={<GridOnIcon />}
                  label={`${scanResult.foundInResults}/${scanResult.totalSearches} Found`}
                  color={
                    scanResult.visibilityPercentage > 50 ? "success" : "warning"
                  }
                  variant="outlined"
                />
              </Stack>
            )}
          </Stack>
        </Card>
      </Box>

      {/* Loading Overlay */}
      {(loading || !isGoogleMapsLoaded) && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            backgroundColor: "rgba(255, 255, 255, 0.8)",
            zIndex: 1000,
          }}
        >
          <CircularProgress />
          <Typography variant="body2" sx={{ mt: 2 }}>
            {!isGoogleMapsLoaded ? "Loading Google Maps..." : "Loading..."}
          </Typography>
        </Box>
      )}

      {/* Map Container */}
      <Box
        ref={mapRef}
        sx={{
          width: "100%",
          height: "100%",
          borderRadius: 1,
          overflow: "hidden",
        }}
      />

      {/* Legend */}
      {effectiveGridPoints.length > 0 && (
        <Box sx={{ position: "absolute", bottom: 16, left: 16, zIndex: 1000 }}>
          <Card sx={{ p: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              Ranking Legend
            </Typography>
            <Stack spacing={1}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Box
                  sx={{
                    width: 16,
                    height: 16,
                    borderRadius: "50%",
                    backgroundColor: "#4CAF50",
                    border: "2px solid #ffffff",
                  }}
                />
                <Typography variant="caption">#1</Typography>
              </Stack>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Box
                  sx={{
                    width: 16,
                    height: 16,
                    borderRadius: "50%",
                    backgroundColor: "#8BC34A",
                    border: "2px solid #ffffff",
                  }}
                />
                <Typography variant="caption">Top 3</Typography>
              </Stack>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Box
                  sx={{
                    width: 16,
                    height: 16,
                    borderRadius: "50%",
                    backgroundColor: "#FFC107",
                    border: "2px solid #ffffff",
                  }}
                />
                <Typography variant="caption">Top 5</Typography>
              </Stack>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Box
                  sx={{
                    width: 16,
                    height: 16,
                    borderRadius: "50%",
                    backgroundColor: "#FF9800",
                    border: "2px solid #ffffff",
                  }}
                />
                <Typography variant="caption">Top 10</Typography>
              </Stack>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Box
                  sx={{
                    width: 16,
                    height: 16,
                    borderRadius: "50%",
                    backgroundColor: "#F44336",
                    border: "2px solid #ffffff",
                  }}
                />
                <Typography variant="caption">Not Found</Typography>
              </Stack>
            </Stack>
          </Card>
        </Box>
      )}

      {/* No Data Message */}
      {!loading && !center && isGoogleMapsLoaded && (
        <Box
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            textAlign: "center",
          }}
        >
          <LocationOnIcon
            sx={{ fontSize: 48, color: "text.secondary", mb: 2 }}
          />
          <Typography variant="h6" color="text.secondary">
            Search for a location to begin
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Enter a location and configure your scan settings
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default LocalFalconMap;
