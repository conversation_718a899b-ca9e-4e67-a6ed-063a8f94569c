const pool = require("../config/db");
const logger = require("../utils/logger");

module.exports = class Instagram {
  /**
   * Save Instagram OAuth tokens
   * @param {Object} tokenData - Token data
   * @returns {Promise<Object>} Save result
   */
  static async saveOAuthTokens(tokenData) {
    try {
      const query = `
        INSERT INTO instagram_oauth_tokens
        (user_id, instagram_user_id, instagram_user_name, instagram_user_email, instagram_user_picture, access_token, refresh_token, expires_at, status_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
        ON DUPLICATE KEY UPDATE
        instagram_user_name = VALUES(instagram_user_name),
        instagram_user_email = VALUES(instagram_user_email),
        instagram_user_picture = VALUES(instagram_user_picture),
        access_token = VALUES(access_token),
        refresh_token = VALUES(refresh_token),
        expires_at = VALUES(expires_at),
        status_id = 1,
        updated_at = CURRENT_TIMESTAMP
      `;

      const values = [
        tokenData.userId,
        tokenData.instagramUserId,
        tokenData.instagramUserName || null,
        tokenData.instagramUserEmail || null,
        tokenData.instagramUserPicture || null,
        tokenData.accessToken,
        tokenData.refreshToken || null,
        tokenData.expiresAt || null,
      ];

      const result = await pool.query(query, values);

      logger.info("Instagram OAuth tokens saved", {
        userId: tokenData.userId,
        instagramUserId: tokenData.instagramUserId,
        insertId: result.insertId,
      });

      return { success: true, result };
    } catch (error) {
      logger.error("Error saving Instagram OAuth tokens:", {
        error: error.message,
        tokenData: { ...tokenData, accessToken: "[REDACTED]" },
      });
      throw error;
    }
  }

  /**
   * Save Instagram accounts
   * @param {Array} accountsData - Array of account data
   * @returns {Promise<Object>} Save result
   */
  static async saveAccounts(accountsData) {
    try {
      const query = `
        INSERT INTO instagram_accounts
        (instagram_oauth_token_id, account_id, account_name, account_username, account_picture_url, account_type)
        VALUES (?, ?, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
        account_name = VALUES(account_name),
        account_username = VALUES(account_username),
        account_picture_url = VALUES(account_picture_url),
        account_type = VALUES(account_type),
        is_active = 1,
        updated_at = CURRENT_TIMESTAMP
      `;

      const promises = accountsData.map((account) => {
        const values = [
          account.instagramOAuthTokenId,
          account.accountId,
          account.accountName,
          account.accountUsername || null,
          account.accountPictureUrl || null,
          account.accountType || "personal",
        ];
        return pool.query(query, values);
      });

      await Promise.all(promises);

      logger.info("Instagram accounts saved", {
        accountsCount: accountsData.length,
      });

      return { success: true };
    } catch (error) {
      logger.error("Error saving Instagram accounts:", {
        error: error.message,
        accountsCount: accountsData.length,
      });
      throw error;
    }
  }

  /**
   * Get Instagram accounts for user
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Instagram accounts result
   */
  static async getInstagramAccounts(userId) {
    try {
      const query = `
        SELECT id, user_id, instagram_user_id, instagram_user_name, instagram_user_email,
               instagram_user_picture, created_at, updated_at
        FROM instagram_oauth_tokens
        WHERE user_id = ? AND status_id = 1
        ORDER BY created_at DESC
      `;

      const results = await pool.query(query, [userId]);

      logger.info("Instagram accounts retrieved", {
        userId,
        accountsCount: results.length,
      });

      return { success: true, accounts: results };
    } catch (error) {
      logger.error("Error getting Instagram accounts:", {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Get Instagram accounts/pages for user
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Accounts result
   */
  static async getAccounts(userId) {
    try {
      const query = `
        SELECT ia.*, iot.instagram_user_name, iot.instagram_user_email
        FROM instagram_accounts ia
        LEFT JOIN instagram_oauth_tokens iot ON ia.instagram_oauth_token_id = iot.id
        WHERE iot.user_id = ? AND ia.is_active = 1 AND iot.status_id = 1
        ORDER BY ia.created_at DESC
      `;

      const results = await pool.query(query, [userId]);

      logger.info("Instagram accounts retrieved", {
        userId,
        accountsCount: results.length,
      });

      return { success: true, accounts: results };
    } catch (error) {
      logger.error("Error getting Instagram accounts:", {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Save Instagram post
   * @param {Object} postData - Post data
   * @returns {Promise<Object>} Save result
   */
  static async savePost(postData) {
    try {
      const query = `
        INSERT INTO instagram_posts
        (user_id, account_id, instagram_post_id, bulk_post_id, is_bulk_post, post_content, post_response,
         caption, media_url, media_type, published, scheduled_publish_time, status, instagram_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        postData.userId,
        postData.accountId,
        postData.instagramPostId || null,
        postData.bulkPostId || null,
        postData.isBulkPost || false,
        JSON.stringify(postData.postContent),
        JSON.stringify(postData.postResponse || {}),
        postData.caption || null,
        postData.mediaUrl || null,
        postData.mediaType || "image",
        postData.published !== false,
        postData.scheduledPublishTime || null,
        postData.status || "published",
        postData.instagramUrl || null,
      ];

      const result = await pool.query(query, values);

      logger.info("Instagram post saved", {
        userId: postData.userId,
        accountId: postData.accountId,
        postId: result.insertId,
      });

      return { success: true, postId: result.insertId };
    } catch (error) {
      logger.error("Error saving Instagram post:", {
        error: error.message,
        userId: postData.userId,
        accountId: postData.accountId,
      });
      throw error;
    }
  }

  /**
   * Get Instagram posts
   * @param {number} userId - User ID
   * @param {string} accountId - Account ID (optional)
   * @param {number} page - Page number
   * @param {number} limit - Limit per page
   * @returns {Promise<Object>} Posts result
   */
  static async getPosts(userId, accountId = null, page = 1, limit = 10) {
    try {
      let query = `
        SELECT ip.*, ia.account_name, iot.instagram_user_name, iot.instagram_user_email
        FROM instagram_posts ip
        LEFT JOIN instagram_accounts ia ON ip.account_id = ia.account_id
        LEFT JOIN instagram_oauth_tokens iot ON ia.instagram_oauth_token_id = iot.id
        WHERE ip.user_id = ?
      `;
      let params = [userId];

      if (accountId) {
        query += ` AND ip.account_id = ?`;
        params.push(accountId);
      }

      query += ` ORDER BY ip.created_at DESC`;

      // Add pagination
      const offset = (page - 1) * limit;
      query += ` LIMIT ? OFFSET ?`;
      params.push(limit, offset);

      const results = await pool.query(query, params);

      // Get total count for pagination
      let countQuery = `
        SELECT COUNT(*) as total
        FROM instagram_posts ip
        WHERE ip.user_id = ?
      `;
      let countParams = [userId];

      if (accountId) {
        countQuery += ` AND ip.account_id = ?`;
        countParams.push(accountId);
      }

      const countResult = await pool.query(countQuery, countParams);
      const total = countResult[0]?.total || 0;

      logger.info("Instagram posts retrieved", {
        userId,
        accountId,
        page,
        limit,
        total,
        resultsCount: results.length,
      });

      return {
        success: true,
        posts: results,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error("Error getting Instagram posts:", {
        error: error.message,
        userId,
        accountId,
        page,
        limit,
      });
      throw error;
    }
  }

  /**
   * Get Instagram post by ID
   * @param {number} postId - Post ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Post result
   */
  static async getPostById(postId, userId) {
    try {
      const query = `
        SELECT ip.*, ia.account_name, iot.instagram_user_name, iot.instagram_user_email
        FROM instagram_posts ip
        LEFT JOIN instagram_accounts ia ON ip.account_id = ia.account_id
        LEFT JOIN instagram_oauth_tokens iot ON ia.instagram_oauth_token_id = iot.id
        WHERE ip.id = ? AND ip.user_id = ?
      `;

      const results = await pool.query(query, [postId, userId]);

      if (results.length === 0) {
        return { success: false, message: "Post not found" };
      }

      logger.info("Instagram post retrieved by ID", {
        postId,
        userId,
      });

      return { success: true, post: results[0] };
    } catch (error) {
      logger.error("Error getting Instagram post by ID:", {
        error: error.message,
        postId,
        userId,
      });
      throw error;
    }
  }

  /**
   * Update Instagram post status
   * @param {number} postId - Post ID
   * @param {number} userId - User ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Update result
   */
  static async updatePostStatus(postId, userId, status) {
    try {
      const query = `
        UPDATE instagram_posts
        SET status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ? AND user_id = ?
      `;

      const result = await pool.query(query, [status, postId, userId]);

      if (result.affectedRows === 0) {
        return { success: false, message: "Post not found or not authorized" };
      }

      logger.info("Instagram post status updated", {
        postId,
        userId,
        status,
      });

      return { success: true };
    } catch (error) {
      logger.error("Error updating Instagram post status:", {
        error: error.message,
        postId,
        userId,
        status,
      });
      throw error;
    }
  }

  /**
   * Delete Instagram post
   * @param {number} postId - Post ID
   * @param {number} userId - User ID
   * @returns {Promise<Object>} Delete result
   */
  static async deletePost(postId, userId) {
    try {
      const query = `
        DELETE FROM instagram_posts
        WHERE id = ? AND user_id = ?
      `;

      const result = await pool.query(query, [postId, userId]);

      if (result.affectedRows === 0) {
        return { success: false, message: "Post not found or not authorized" };
      }

      logger.info("Instagram post deleted", {
        postId,
        userId,
      });

      return { success: true };
    } catch (error) {
      logger.error("Error deleting Instagram post:", {
        error: error.message,
        postId,
        userId,
      });
      throw error;
    }
  }
};
