{"timestamp":"2025-07-25T04:19:14.102Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"12424462...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-25T04:19:14.192Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-25T04:19:16.181Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-24T22:49:16.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-25T04:19:33.828Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"5c6945ef-90c1-46d8-9ed4-8c6a29559cff","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T04:19:33.855Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c9e9c5bb-5aa9-4022-bb39-4b2ce6002702","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T04:19:33.862Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"c9e9c5bb-5aa9-4022-bb39-4b2ce6002702","userId":"132"}
{"timestamp":"2025-07-25T04:19:33.883Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6e93af41-bc60-47e1-b5f9-23aa52470d56","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T04:19:33.904Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"cf8ce6e9-d91f-4ab3-852d-e189f713de97","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-25T04:19:34.244Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-25T04:19:34.247Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"cf8ce6e9-d91f-4ab3-852d-e189f713de97","userId":"132","accountCount":0}
{"timestamp":"2025-07-25T04:19:34.257Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-25T04:19:34.266Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-25T04:19:34.302Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-25T04:19:38.454Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"797fe8a6-578f-472f-b003-5687dd3a694a","controller":"instagram","action":"authenticate"}
{"timestamp":"2025-07-25T04:19:38.456Z","level":"INFO","message":"Instagram authentication initiated","environment":"DEVELOPMENT","requestId":"797fe8a6-578f-472f-b003-5687dd3a694a","userId":132}
{"timestamp":"2025-07-25T04:19:38.463Z","level":"INFO","message":"Instagram OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts"}
{"timestamp":"2025-07-25T04:19:38.469Z","level":"INFO","message":"Instagram authentication URL generated","environment":"DEVELOPMENT","requestId":"797fe8a6-578f-472f-b003-5687dd3a694a","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-25T04:19:52.481Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0726d8aa-71e9-4877-9e7c-f63ceb35f36b","controller":"instagram","action":"callback"}
{"timestamp":"2025-07-25T04:19:56.068Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"586bef15-e31e-4fcb-932c-267ab8c183d5","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-25T04:19:56.071Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"586bef15-e31e-4fcb-932c-267ab8c183d5","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-25T04:19:56.077Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"586bef15-e31e-4fcb-932c-267ab8c183d5","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-25T04:19:56.081Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"586bef15-e31e-4fcb-932c-267ab8c183d5","userId":132}
{"timestamp":"2025-07-25T04:19:56.683Z","level":"INFO","message":"Instagram (Facebook) access token obtained","environment":"DEVELOPMENT","hasAccessToken":true,"tokenType":"bearer"}
{"timestamp":"2025-07-25T04:19:57.102Z","level":"INFO","message":"Facebook user info retrieved for Instagram","environment":"DEVELOPMENT","userId":"3060498*********","hasEmail":true}
{"timestamp":"2025-07-25T04:19:57.104Z","level":"INFO","message":"Instagram user info retrieved","environment":"DEVELOPMENT","requestId":"586bef15-e31e-4fcb-932c-267ab8c183d5","userId":"3060498*********","userName":"Sean Sunny","hasEmail":true}
{"timestamp":"2025-07-25T04:20:06.658Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","totalPages":1}
{"timestamp":"2025-07-25T04:20:06.662Z","level":"INFO","message":"Checking page for Instagram Business account","environment":"DEVELOPMENT","pageId":"***************","pageName":"Integration Testers","pageCategory":"Business Consultant","hasPageAccessToken":true}
{"timestamp":"2025-07-25T04:20:07.964Z","level":"INFO","message":"Requesting Instagram business account for page","environment":"DEVELOPMENT","pageId":"***************","url":"https://graph.facebook.com/v20.0/***************","hasAccessToken":true}
{"timestamp":"2025-07-25T04:20:12.330Z","level":"INFO","message":"Instagram business account API response","environment":"DEVELOPMENT","pageId":"***************","responseData":{"id":"***************"},"hasInstagramBusinessAccount":false}
{"timestamp":"2025-07-25T04:20:23.395Z","level":"INFO","message":"Instagram Business account check result","environment":"DEVELOPMENT","pageId":"***************","pageName":"Integration Testers","hasInstagramAccount":false}
{"timestamp":"2025-07-25T04:20:24.520Z","level":"INFO","message":"Instagram Business accounts retrieved","environment":"DEVELOPMENT","totalPages":1,"instagramAccountsCount":0}
{"timestamp":"2025-07-25T04:20:24.522Z","level":"INFO","message":"Instagram pages retrieved","environment":"DEVELOPMENT","requestId":"586bef15-e31e-4fcb-932c-267ab8c183d5","pagesCount":0,"pageIds":[]}
{"timestamp":"2025-07-25T04:20:24.550Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7415775e-0676-4866-acf3-245610c5d6db","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-25T04:20:24.553Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"7415775e-0676-4866-acf3-245610c5d6db","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-25T04:20:24.555Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"7415775e-0676-4866-acf3-245610c5d6db","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-25T04:20:24.559Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"7415775e-0676-4866-acf3-245610c5d6db","userId":132}
{"timestamp":"2025-07-25T04:20:24.600Z","level":"INFO","message":"Instagram OAuth tokens saved","environment":"DEVELOPMENT","userId":132,"instagramUserId":"3060498*********","insertId":30}
{"timestamp":"2025-07-25T04:20:24.602Z","level":"INFO","message":"Instagram authentication completed successfully","environment":"DEVELOPMENT","requestId":"586bef15-e31e-4fcb-932c-267ab8c183d5","userId":132,"instagramUserId":"3060498*********","instagramUserEmail":"<EMAIL>","accountsCount":0}
{"timestamp":"2025-07-25T04:20:24.954Z","level":"ERROR","message":"Error exchanging code for Instagram token:","environment":"DEVELOPMENT","error":"Request failed with status code 400","response":{"error":{"message":"This authorization code has been used.","type":"OAuthException","code":100,"error_subcode":36009,"fbtrace_id":"AGQYCfX-Ou6LASE6FK7jv-f"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at InstagramService.exchangeCodeForToken (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\instagram.service.js:115:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:217:23)"}
{"timestamp":"2025-07-25T04:20:24.957Z","level":"ERROR","message":"Error in Instagram callback validation","environment":"DEVELOPMENT","requestId":"7415775e-0676-4866-acf3-245610c5d6db","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InstagramService.exchangeCodeForToken (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\instagram.service.js:99:24)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:217:23)"}
{"timestamp":"2025-07-25T04:22:46.738Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-25","endDate":"2025-07-25"}
{"timestamp":"2025-07-25T04:22:46.978Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-25T04:22:49.116Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"469c3af2-05a9-4738-bb38-5e4811bafc08","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T04:22:49.133Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"3c165b9d-cae5-4a7a-9248-b5a1c92ccc9b","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T04:22:49.153Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"********-1f60-48c2-b308-6ff79340dcc9","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T04:22:49.157Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"********-1f60-48c2-b308-6ff79340dcc9","userId":"132"}
{"timestamp":"2025-07-25T04:22:49.174Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"********-af6c-487b-a458-a8bae5558dcd","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-25T04:22:49.184Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-25T04:22:49.206Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-25T04:22:49.218Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-25T04:22:49.246Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-25T04:22:49.249Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"********-af6c-487b-a458-a8bae5558dcd","userId":"132","accountCount":0}
{"timestamp":"2025-07-25T04:22:52.356Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e07e78a7-4421-4274-ae39-1d8b4eef90ac","controller":"instagram","action":"authenticate"}
{"timestamp":"2025-07-25T04:22:52.360Z","level":"INFO","message":"Instagram authentication initiated","environment":"DEVELOPMENT","requestId":"e07e78a7-4421-4274-ae39-1d8b4eef90ac","userId":132}
{"timestamp":"2025-07-25T04:22:52.366Z","level":"INFO","message":"Instagram OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts"}
{"timestamp":"2025-07-25T04:22:52.373Z","level":"INFO","message":"Instagram authentication URL generated","environment":"DEVELOPMENT","requestId":"e07e78a7-4421-4274-ae39-1d8b4eef90ac","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-25T04:22:58.437Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d4d506e4-9203-4b6a-a6ee-e62a701b413e","controller":"instagram","action":"callback"}
{"timestamp":"2025-07-25T04:23:02.071Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"236f2e11-fc1c-464f-9519-cb8888645378","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-25T04:23:02.076Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"236f2e11-fc1c-464f-9519-cb8888645378","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-25T04:23:02.083Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"236f2e11-fc1c-464f-9519-cb8888645378","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-25T04:23:02.088Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"236f2e11-fc1c-464f-9519-cb8888645378","userId":132}
{"timestamp":"2025-07-25T04:23:02.538Z","level":"INFO","message":"Instagram (Facebook) access token obtained","environment":"DEVELOPMENT","hasAccessToken":true,"tokenType":"bearer"}
{"timestamp":"2025-07-25T04:23:03.102Z","level":"INFO","message":"Facebook user info retrieved for Instagram","environment":"DEVELOPMENT","userId":"3060498*********","hasEmail":true}
{"timestamp":"2025-07-25T04:23:03.105Z","level":"INFO","message":"Instagram user info retrieved","environment":"DEVELOPMENT","requestId":"236f2e11-fc1c-464f-9519-cb8888645378","userId":"3060498*********","userName":"Sean Sunny","hasEmail":true}
{"timestamp":"2025-07-25T04:23:09.118Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","totalPages":1}
{"timestamp":"2025-07-25T04:23:09.122Z","level":"INFO","message":"Checking page for Instagram Business account","environment":"DEVELOPMENT","pageId":"***************","pageName":"Integration Testers","pageCategory":"Business Consultant","hasPageAccessToken":true}
{"timestamp":"2025-07-25T04:23:09.981Z","level":"INFO","message":"Requesting Instagram business account for page","environment":"DEVELOPMENT","pageId":"***************","url":"https://graph.facebook.com/v20.0/***************","hasAccessToken":true}
{"timestamp":"2025-07-25T04:23:11.417Z","level":"INFO","message":"Instagram business account API response","environment":"DEVELOPMENT","pageId":"***************","responseData":{"id":"***************"},"hasInstagramBusinessAccount":false}
{"timestamp":"2025-07-25T04:23:12.749Z","level":"INFO","message":"Instagram Business account check result","environment":"DEVELOPMENT","pageId":"***************","pageName":"Integration Testers","hasInstagramAccount":false}
{"timestamp":"2025-07-25T04:23:13.776Z","level":"INFO","message":"Instagram Business accounts retrieved","environment":"DEVELOPMENT","totalPages":1,"instagramAccountsCount":0}
{"timestamp":"2025-07-25T04:23:13.781Z","level":"INFO","message":"Instagram pages retrieved","environment":"DEVELOPMENT","requestId":"236f2e11-fc1c-464f-9519-cb8888645378","pagesCount":0,"pageIds":[]}
{"timestamp":"2025-07-25T04:23:13.820Z","level":"INFO","message":"Instagram OAuth tokens saved","environment":"DEVELOPMENT","userId":132,"instagramUserId":"3060498*********","insertId":30}
{"timestamp":"2025-07-25T04:23:13.822Z","level":"INFO","message":"Instagram authentication completed successfully","environment":"DEVELOPMENT","requestId":"236f2e11-fc1c-464f-9519-cb8888645378","userId":132,"instagramUserId":"3060498*********","instagramUserEmail":"<EMAIL>","accountsCount":0}
{"timestamp":"2025-07-25T04:23:17.618Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8ff34631-0f48-4733-904a-d9c724782b59","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T04:23:17.631Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"32d0f27b-788e-4a16-aa80-8d364758e272","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T04:23:17.634Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"32d0f27b-788e-4a16-aa80-8d364758e272","userId":"132"}
{"timestamp":"2025-07-25T04:23:17.650Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"51764adc-528b-4c6c-a4d2-76f515d02fa2","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-25T04:23:17.665Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4cfcb78c-20c0-49f0-a203-f11f0940c78b","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T04:23:17.678Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-25T04:23:17.684Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-25T04:23:17.714Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-25T04:23:17.717Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"51764adc-528b-4c6c-a4d2-76f515d02fa2","userId":"132","accountCount":0}
{"timestamp":"2025-07-25T04:23:17.726Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-25T04:31:46.353Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c3801393-1970-42f7-8155-e15c800596d3","controller":"instagram","action":"welcome"}
{"timestamp":"2025-07-25T04:43:59.008Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1a0a504f-98da-49f7-b2a9-7ceb256c2913","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-25T04:43:59.013Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"1a0a504f-98da-49f7-b2a9-7ceb256c2913","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-25T04:43:59.021Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"1a0a504f-98da-49f7-b2a9-7ceb256c2913","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-25T04:43:59.024Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"1a0a504f-98da-49f7-b2a9-7ceb256c2913","userId":132}
{"timestamp":"2025-07-25T04:43:59.375Z","level":"ERROR","message":"Error exchanging code for Instagram token:","environment":"DEVELOPMENT","error":"Request failed with status code 400","response":{"error":{"message":"This authorization code has expired.","type":"OAuthException","code":100,"error_subcode":36007,"fbtrace_id":"Ak0Rkjd3pYWEKiJMKZN5o2H"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at InstagramService.exchangeCodeForToken (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\instagram.service.js:115:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:217:23)"}
{"timestamp":"2025-07-25T04:43:59.380Z","level":"ERROR","message":"Error in Instagram callback validation","environment":"DEVELOPMENT","requestId":"1a0a504f-98da-49f7-b2a9-7ceb256c2913","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InstagramService.exchangeCodeForToken (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\instagram.service.js:99:24)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:217:23)"}
{"timestamp":"2025-07-25T06:12:36.905Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-25","endDate":"2025-07-25"}
{"timestamp":"2025-07-25T06:12:37.075Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-25T06:12:42.614Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"eeb9bf27-6df4-48ff-b795-97a0013adcb9","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T06:12:42.625Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"574ab2ad-a20f-44dd-a0e6-768f76d633c7","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T06:12:42.634Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"cf056875-7d5b-467a-8b6c-8588f0e2c1ef","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T06:12:42.637Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"cf056875-7d5b-467a-8b6c-8588f0e2c1ef","userId":"132"}
{"timestamp":"2025-07-25T06:12:42.646Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6daf6956-8b0f-4000-b57f-b255bf39f8cd","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-25T06:12:42.652Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-25T06:12:42.658Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":0}
{"timestamp":"2025-07-25T06:12:42.664Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-25T06:12:42.676Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-25T06:12:42.678Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"6daf6956-8b0f-4000-b57f-b255bf39f8cd","userId":"132","accountCount":0}
{"timestamp":"2025-07-25T06:13:11.279Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"526d05b7-ddc3-4a09-8647-d71ee75f86dd","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-25T06:13:11.282Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"526d05b7-ddc3-4a09-8647-d71ee75f86dd","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-25T06:13:11.849Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"526d05b7-ddc3-4a09-8647-d71ee75f86dd","userId":132,"email":"<EMAIL>"}
{"timestamp":"2025-07-25T06:13:13.536Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-25","endDate":"2025-07-25"}
{"timestamp":"2025-07-25T06:13:13.674Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-25T06:13:36.891Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"82432ef2-a507-463f-9983-5641354c48ee","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-25T06:13:36.894Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"82432ef2-a507-463f-9983-5641354c48ee","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-25T06:13:37.028Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"82432ef2-a507-463f-9983-5641354c48ee","userId":52,"email":"<EMAIL>"}
{"timestamp":"2025-07-25T06:14:38.728Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d5eb9636-e878-43fb-87f8-31373ff0103a","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-25T06:14:38.732Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"d5eb9636-e878-43fb-87f8-31373ff0103a","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-25T06:14:39.161Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"d5eb9636-e878-43fb-87f8-31373ff0103a","userId":130,"email":"<EMAIL>"}
{"timestamp":"2025-07-25T06:14:45.673Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9ffd05f5-2a1b-4c95-a88d-0e8e91531788","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T06:14:45.681Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"04e57fc3-edbf-448c-95c4-457b95e44183","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T06:14:45.684Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"04e57fc3-edbf-448c-95c4-457b95e44183","userId":"130"}
{"timestamp":"2025-07-25T06:14:45.691Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6da8cd28-dddc-4bd1-98e0-054f441a8a1a","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T06:14:45.698Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"03c9c7f5-5782-4e9a-abb4-9615fe33e4bc","controller":"twitter","action":"getTwitterAccounts","userId":"130"}
{"timestamp":"2025-07-25T06:14:45.718Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":0}
{"timestamp":"2025-07-25T06:14:45.731Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":0}
{"timestamp":"2025-07-25T06:14:45.740Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":130,"pagesCount":0}
{"timestamp":"2025-07-25T06:14:45.748Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":130,"accountCount":0}
{"timestamp":"2025-07-25T06:14:45.750Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"03c9c7f5-5782-4e9a-abb4-9615fe33e4bc","userId":"130","accountCount":0}
{"timestamp":"2025-07-25T06:14:46.898Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"55261ff0-f9e5-4edb-8ccb-3c3743cbd301","controller":"linkedin","action":"authenticate"}
{"timestamp":"2025-07-25T06:14:46.900Z","level":"INFO","message":"LinkedIn authentication initiated","environment":"DEVELOPMENT","requestId":"55261ff0-f9e5-4edb-8ccb-3c3743cbd301","userId":130}
{"timestamp":"2025-07-25T06:14:46.902Z","level":"INFO","message":"LinkedIn OAuth URL generated","environment":"DEVELOPMENT","userId":130,"scopes":"openid, profile, w_member_social, email"}
{"timestamp":"2025-07-25T06:14:46.905Z","level":"INFO","message":"LinkedIn authentication URL generated","environment":"DEVELOPMENT","requestId":"55261ff0-f9e5-4edb-8ccb-3c3743cbd301","userId":130,"hasAuthUrl":true}
{"timestamp":"2025-07-25T06:15:14.674Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d962814b-fc3a-4608-846e-93461372f81e","controller":"linkedin","action":"callback"}
{"timestamp":"2025-07-25T06:15:18.353Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"00e50f2e-f115-4227-8102-34db0238a8de","controller":"linkedin","action":"callbackValidation"}
{"timestamp":"2025-07-25T06:15:18.921Z","level":"INFO","message":"LinkedIn access token obtained successfully","environment":"DEVELOPMENT","hasAccessToken":true,"expiresIn":5183999}
{"timestamp":"2025-07-25T06:15:20.099Z","level":"INFO","message":"Limited profile access with w_member_social scope","environment":"DEVELOPMENT","error":"Request failed with status code 403"}
{"timestamp":"2025-07-25T06:15:20.370Z","level":"ERROR","message":"Error getting LinkedIn user email","environment":"DEVELOPMENT","userId":"7407b47948074bb947fcf356a90e21b0","error":"Request failed with status code 403","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at LinkedInService.getUserProfile (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\linkedin.service.js:160:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\linkedin.controller.js:206:30)"}
{"timestamp":"2025-07-25T06:15:20.372Z","level":"INFO","message":"LinkedIn user profile retrieved successfully","environment":"DEVELOPMENT","userId":"7407b47948074bb947fcf356a90e21b0","hasEmail":false,"fullProfile":"{\n  \"id\": \"7407b47948074bb947fcf356a90e21b0\"\n}"}
{"timestamp":"2025-07-25T06:15:25.029Z","level":"INFO","message":"LinkedIn OAuth tokens saved successfully","environment":"DEVELOPMENT","userId":130,"linkedinUserId":"7407b47948074bb947fcf356a90e21b0","linkedinUserEmail":null}
{"timestamp":"2025-07-25T06:15:25.030Z","level":"INFO","message":"LinkedIn authentication completed successfully","environment":"DEVELOPMENT","requestId":"00e50f2e-f115-4227-8102-34db0238a8de","userId":130,"linkedinUserId":"7407b47948074bb947fcf356a90e21b0","linkedinUserEmail":null}
{"timestamp":"2025-07-25T06:15:28.536Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b157c59a-7e8a-487e-8f46-54493aa33517","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T06:15:28.544Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"********-3894-475f-8373-8920d181de78","controller":"twitter","action":"getTwitterAccounts","userId":"130"}
{"timestamp":"2025-07-25T06:15:28.552Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"3e4750d1-8e96-4bf8-bb37-9367de06a415","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T06:15:28.559Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"3a044485-ceb1-484d-a007-deeee0a3d9d3","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T06:15:28.562Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"3a044485-ceb1-484d-a007-deeee0a3d9d3","userId":"130"}
{"timestamp":"2025-07-25T06:15:28.572Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":130,"pagesCount":0}
{"timestamp":"2025-07-25T06:15:28.578Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":130,"accountCount":0}
{"timestamp":"2025-07-25T06:15:28.583Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"********-3894-475f-8373-8920d181de78","userId":"130","accountCount":0}
{"timestamp":"2025-07-25T06:15:28.588Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":1}
{"timestamp":"2025-07-25T06:15:28.592Z","level":"INFO","message":"LinkedIn pages retrieved successfully","environment":"DEVELOPMENT","requestId":"3e4750d1-8e96-4bf8-bb37-9367de06a415","userId":"130","pagesCount":1}
{"timestamp":"2025-07-25T06:15:28.602Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":0}
{"timestamp":"2025-07-25T06:15:32.254Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"586c0b25-b831-4e72-9419-e2834b83ea83","controller":"business","action":"businessList","userId":"130"}
{"timestamp":"2025-07-25T06:15:32.256Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"586c0b25-b831-4e72-9419-e2834b83ea83","userId":"130"}
{"timestamp":"2025-07-25T06:15:32.315Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"586c0b25-b831-4e72-9419-e2834b83ea83","userId":"130","businessCount":0}
{"timestamp":"2025-07-25T06:15:43.954Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"********-dfe2-4715-b900-d8a327a9bfeb","controller":"business","action":"businessList","userId":"130"}
{"timestamp":"2025-07-25T06:15:43.957Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"********-dfe2-4715-b900-d8a327a9bfeb","userId":"130"}
{"timestamp":"2025-07-25T06:15:44.016Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"********-dfe2-4715-b900-d8a327a9bfeb","userId":"130","businessCount":0}
{"timestamp":"2025-07-25T06:24:27.126Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"51d36ecb-3691-4e45-a944-22000d359ed9","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T06:24:27.137Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"20fd61f0-4a48-4ca7-bb42-67bc847ba59a","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T06:24:27.141Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"20fd61f0-4a48-4ca7-bb42-67bc847ba59a","userId":"130"}
{"timestamp":"2025-07-25T06:24:27.162Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8b41a638-d25c-4ab8-91ad-0914ce2ad793","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T06:24:27.173Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"444c21af-f21a-4bda-8865-af5ad55f5b0f","controller":"twitter","action":"getTwitterAccounts","userId":"130"}
{"timestamp":"2025-07-25T06:24:27.180Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":130,"pagesCount":0}
{"timestamp":"2025-07-25T06:24:27.184Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":0}
{"timestamp":"2025-07-25T06:24:27.200Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":1}
{"timestamp":"2025-07-25T06:24:27.202Z","level":"INFO","message":"LinkedIn pages retrieved successfully","environment":"DEVELOPMENT","requestId":"8b41a638-d25c-4ab8-91ad-0914ce2ad793","userId":"130","pagesCount":1}
{"timestamp":"2025-07-25T06:24:27.208Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":130,"accountCount":0}
{"timestamp":"2025-07-25T06:24:27.209Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"444c21af-f21a-4bda-8865-af5ad55f5b0f","userId":"130","accountCount":0}
{"timestamp":"2025-07-25T06:25:24.188Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c2ca5d4b-4b39-4871-94b5-713197c9a8c7","controller":"linkedin","action":"createPost"}
{"timestamp":"2025-07-25T06:25:24.220Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":1}
{"timestamp":"2025-07-25T06:25:25.312Z","level":"WARN","message":"Could not get LinkedIn profile for posting, using fallback","environment":"DEVELOPMENT","error":"Request failed with status code 403"}
{"timestamp":"2025-07-25T06:25:25.314Z","level":"INFO","message":"Media provided but skipping for LinkedIn post","environment":"DEVELOPMENT","mediaCount":1,"profileId":"7407b47948074bb947fcf356a90e21b0"}
{"timestamp":"2025-07-25T06:25:25.316Z","level":"INFO","message":"Attempting to create LinkedIn post","environment":"DEVELOPMENT","profileId":"7407b47948074bb947fcf356a90e21b0","postPayload":"{\n  \"author\": \"urn:li:person:~\",\n  \"lifecycleState\": \"PUBLISHED\",\n  \"specificContent\": {\n    \"com.linkedin.ugc.ShareContent\": {\n      \"shareCommentary\": {\n        \"text\": \"Test data what.\"\n      },\n      \"shareMediaCategory\": \"NONE\"\n    }\n  },\n  \"visibility\": {\n    \"com.linkedin.ugc.MemberNetworkVisibility\": \"PUBLIC\"\n  }\n}"}
{"timestamp":"2025-07-25T06:25:25.584Z","level":"ERROR","message":"Error creating LinkedIn post","environment":"DEVELOPMENT","error":"Request failed with status code 422","response":{"message":"ERROR :: /author :: \"urn:li:person:~\" does not match urn:li:company:\\d+|urn:li:member:\\d+\n","status":422},"profileId":"7407b47948074bb947fcf356a90e21b0","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at LinkedInService.createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\linkedin.service.js:318:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\linkedin.controller.js:421:24)"}
{"timestamp":"2025-07-25T06:27:12.311Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b9f0f078-6a90-4510-b53c-666b5dd40e61","controller":"twitter","action":"getTwitterAccounts","userId":"130"}
{"timestamp":"2025-07-25T06:27:12.341Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"dc120348-aadf-45ce-92ff-e3c6970917cd","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T06:27:12.390Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1603fd5f-7132-410d-99c1-6c036c378cda","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T06:27:12.396Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"1603fd5f-7132-410d-99c1-6c036c378cda","userId":"130"}
{"timestamp":"2025-07-25T06:27:12.419Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1234cde6-6474-4366-add4-94e786354e5f","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T06:27:12.438Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":130,"accountCount":0}
{"timestamp":"2025-07-25T06:27:12.442Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"b9f0f078-6a90-4510-b53c-666b5dd40e61","userId":"130","accountCount":0}
{"timestamp":"2025-07-25T06:27:12.448Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":130,"pagesCount":0}
{"timestamp":"2025-07-25T06:27:12.454Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":0}
{"timestamp":"2025-07-25T06:27:12.465Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":0}
{"timestamp":"2025-07-25T06:27:14.400Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c8da2a79-dd45-4c56-a719-18ac31e9caa7","controller":"linkedin","action":"authenticate"}
{"timestamp":"2025-07-25T06:27:14.402Z","level":"INFO","message":"LinkedIn authentication initiated","environment":"DEVELOPMENT","requestId":"c8da2a79-dd45-4c56-a719-18ac31e9caa7","userId":130}
{"timestamp":"2025-07-25T06:27:14.405Z","level":"INFO","message":"LinkedIn OAuth URL generated","environment":"DEVELOPMENT","userId":130,"scopes":"openid, profile, w_member_social, email"}
{"timestamp":"2025-07-25T06:27:14.407Z","level":"INFO","message":"LinkedIn authentication URL generated","environment":"DEVELOPMENT","requestId":"c8da2a79-dd45-4c56-a719-18ac31e9caa7","userId":130,"hasAuthUrl":true}
{"timestamp":"2025-07-25T06:27:14.866Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4f895880-ed9f-431d-8775-8f2d70a1eb54","controller":"linkedin","action":"callback"}
{"timestamp":"2025-07-25T06:27:18.101Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a5176601-33d0-4e63-aa20-3166a193c343","controller":"linkedin","action":"callbackValidation"}
{"timestamp":"2025-07-25T06:27:18.639Z","level":"INFO","message":"LinkedIn access token obtained successfully","environment":"DEVELOPMENT","hasAccessToken":true,"expiresIn":5184000}
{"timestamp":"2025-07-25T06:27:19.556Z","level":"INFO","message":"Limited profile access with w_member_social scope","environment":"DEVELOPMENT","error":"Request failed with status code 403"}
{"timestamp":"2025-07-25T06:27:19.973Z","level":"ERROR","message":"Error getting LinkedIn user email","environment":"DEVELOPMENT","userId":"b8c99cb18c9608e567081b8690b990b5","error":"Request failed with status code 403","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at LinkedInService.getUserProfile (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\linkedin.service.js:160:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\linkedin.controller.js:206:30)"}
{"timestamp":"2025-07-25T06:27:19.976Z","level":"INFO","message":"LinkedIn user profile retrieved successfully","environment":"DEVELOPMENT","userId":"b8c99cb18c9608e567081b8690b990b5","hasEmail":false,"fullProfile":"{\n  \"id\": \"b8c99cb18c9608e567081b8690b990b5\"\n}"}
{"timestamp":"2025-07-25T06:27:23.338Z","level":"INFO","message":"LinkedIn OAuth tokens saved successfully","environment":"DEVELOPMENT","userId":130,"linkedinUserId":"b8c99cb18c9608e567081b8690b990b5","linkedinUserEmail":null}
{"timestamp":"2025-07-25T06:27:23.340Z","level":"INFO","message":"LinkedIn authentication completed successfully","environment":"DEVELOPMENT","requestId":"a5176601-33d0-4e63-aa20-3166a193c343","userId":130,"linkedinUserId":"b8c99cb18c9608e567081b8690b990b5","linkedinUserEmail":null}
{"timestamp":"2025-07-25T06:27:28.050Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6b9d0d57-5ede-4f30-9589-05f668d4d605","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T06:27:28.055Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7d0fed7e-3083-44cd-b6fc-c7ac7139a955","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T06:27:28.060Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b66e3e7e-a33a-450a-b386-c62b59ba9727","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T06:27:28.063Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"b66e3e7e-a33a-450a-b386-c62b59ba9727","userId":"130"}
{"timestamp":"2025-07-25T06:27:28.068Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f98a139b-38aa-4269-a55b-9f6b95381d00","controller":"twitter","action":"getTwitterAccounts","userId":"130"}
{"timestamp":"2025-07-25T06:27:28.080Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":130,"pagesCount":0}
{"timestamp":"2025-07-25T06:27:28.096Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":1}
{"timestamp":"2025-07-25T06:27:28.098Z","level":"INFO","message":"LinkedIn pages retrieved successfully","environment":"DEVELOPMENT","requestId":"7d0fed7e-3083-44cd-b6fc-c7ac7139a955","userId":"130","pagesCount":1}
{"timestamp":"2025-07-25T06:27:28.101Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":0}
{"timestamp":"2025-07-25T06:27:28.105Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":130,"accountCount":0}
{"timestamp":"2025-07-25T06:27:28.106Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"f98a139b-38aa-4269-a55b-9f6b95381d00","userId":"130","accountCount":0}
{"timestamp":"2025-07-25T06:27:33.744Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f7593981-2e81-4c33-aeb1-fd8557f9d13a","controller":"business","action":"businessList","userId":"130"}
{"timestamp":"2025-07-25T06:27:33.746Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"f7593981-2e81-4c33-aeb1-fd8557f9d13a","userId":"130"}
{"timestamp":"2025-07-25T06:27:33.815Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"f7593981-2e81-4c33-aeb1-fd8557f9d13a","userId":"130","businessCount":0}
{"timestamp":"2025-07-25T06:28:10.836Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"618a3776-c83f-43ea-b5cd-690c9e685c4b","controller":"linkedin","action":"createPost"}
{"timestamp":"2025-07-25T06:28:10.862Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":1}
{"timestamp":"2025-07-25T06:28:11.790Z","level":"WARN","message":"Could not get LinkedIn profile for posting, using fallback","environment":"DEVELOPMENT","error":"Request failed with status code 403"}
{"timestamp":"2025-07-25T06:28:11.791Z","level":"INFO","message":"Media provided but skipping for LinkedIn post","environment":"DEVELOPMENT","mediaCount":1,"profileId":"b8c99cb18c9608e567081b8690b990b5"}
{"timestamp":"2025-07-25T06:28:11.793Z","level":"INFO","message":"Attempting to create LinkedIn post","environment":"DEVELOPMENT","profileId":"b8c99cb18c9608e567081b8690b990b5","postPayload":"{\n  \"author\": \"urn:li:person:~\",\n  \"lifecycleState\": \"PUBLISHED\",\n  \"specificContent\": {\n    \"com.linkedin.ugc.ShareContent\": {\n      \"shareCommentary\": {\n        \"text\": \"Test Test Test Test Test\"\n      },\n      \"shareMediaCategory\": \"NONE\"\n    }\n  },\n  \"visibility\": {\n    \"com.linkedin.ugc.MemberNetworkVisibility\": \"PUBLIC\"\n  }\n}"}
{"timestamp":"2025-07-25T06:28:12.224Z","level":"ERROR","message":"Error creating LinkedIn post","environment":"DEVELOPMENT","error":"Request failed with status code 422","response":{"message":"ERROR :: /author :: \"urn:li:person:~\" does not match urn:li:company:\\d+|urn:li:member:\\d+\n","status":422},"profileId":"b8c99cb18c9608e567081b8690b990b5","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at LinkedInService.createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\linkedin.service.js:318:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\linkedin.controller.js:421:24)"}
{"timestamp":"2025-07-25T06:28:26.864Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"16848ab6-5cfd-4148-b4f8-9783bde7b92d","controller":"linkedin","action":"callback"}
{"timestamp":"2025-07-25T06:29:41.459Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8cb074e0-0a3e-41f0-85db-ae55c647c3f0","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T06:29:41.464Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"8cb074e0-0a3e-41f0-85db-ae55c647c3f0","userId":"130"}
{"timestamp":"2025-07-25T06:29:41.475Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0b1f5d1d-dd5e-4af5-845a-5cacc004e79a","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T06:29:41.489Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9f454df0-d912-45f3-ab89-4ddb3ce8b989","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T06:29:41.501Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"254a2318-2a33-4e5c-9811-b6d5718735df","controller":"twitter","action":"getTwitterAccounts","userId":"130"}
{"timestamp":"2025-07-25T06:29:41.509Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":0}
{"timestamp":"2025-07-25T06:29:41.516Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":130,"pagesCount":0}
{"timestamp":"2025-07-25T06:29:41.543Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":0}
{"timestamp":"2025-07-25T06:29:41.554Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":130,"accountCount":0}
{"timestamp":"2025-07-25T06:29:41.555Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"254a2318-2a33-4e5c-9811-b6d5718735df","userId":"130","accountCount":0}
{"timestamp":"2025-07-25T06:29:43.207Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"44a8c334-a4be-47c8-8782-8ef1ddf97160","controller":"linkedin","action":"authenticate"}
{"timestamp":"2025-07-25T06:29:43.209Z","level":"INFO","message":"LinkedIn authentication initiated","environment":"DEVELOPMENT","requestId":"44a8c334-a4be-47c8-8782-8ef1ddf97160","userId":130}
{"timestamp":"2025-07-25T06:29:43.212Z","level":"INFO","message":"LinkedIn OAuth URL generated","environment":"DEVELOPMENT","userId":130,"scopes":"openid, profile, w_member_social, email"}
{"timestamp":"2025-07-25T06:29:43.214Z","level":"INFO","message":"LinkedIn authentication URL generated","environment":"DEVELOPMENT","requestId":"44a8c334-a4be-47c8-8782-8ef1ddf97160","userId":130,"hasAuthUrl":true}
{"timestamp":"2025-07-25T06:29:46.745Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"57735c0b-af13-4db9-9740-71f1db7b89c9","controller":"linkedin","action":"callback"}
{"timestamp":"2025-07-25T06:29:50.431Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9e8bed7b-dcda-485b-9675-e642f0b0330d","controller":"linkedin","action":"callbackValidation"}
{"timestamp":"2025-07-25T06:29:50.938Z","level":"INFO","message":"LinkedIn access token obtained successfully","environment":"DEVELOPMENT","hasAccessToken":true,"expiresIn":5183999}
{"timestamp":"2025-07-25T06:29:52.079Z","level":"INFO","message":"Limited profile access with w_member_social scope","environment":"DEVELOPMENT","error":"Request failed with status code 403"}
{"timestamp":"2025-07-25T06:29:52.579Z","level":"ERROR","message":"Error getting LinkedIn user email","environment":"DEVELOPMENT","userId":"0d22d0f57406e19c089c6940b7617b57","error":"Request failed with status code 403","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at LinkedInService.getUserProfile (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\linkedin.service.js:160:16)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\linkedin.controller.js:206:30)"}
{"timestamp":"2025-07-25T06:29:52.583Z","level":"INFO","message":"LinkedIn user profile retrieved successfully","environment":"DEVELOPMENT","userId":"0d22d0f57406e19c089c6940b7617b57","hasEmail":false,"fullProfile":"{\n  \"id\": \"0d22d0f57406e19c089c6940b7617b57\"\n}"}
{"timestamp":"2025-07-25T06:30:00.831Z","level":"INFO","message":"LinkedIn OAuth tokens saved successfully","environment":"DEVELOPMENT","userId":130,"linkedinUserId":"0d22d0f57406e19c089c6940b7617b57","linkedinUserEmail":null}
{"timestamp":"2025-07-25T06:30:00.833Z","level":"INFO","message":"LinkedIn authentication completed successfully","environment":"DEVELOPMENT","requestId":"9e8bed7b-dcda-485b-9675-e642f0b0330d","userId":130,"linkedinUserId":"0d22d0f57406e19c089c6940b7617b57","linkedinUserEmail":null}
{"timestamp":"2025-07-25T06:30:04.482Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e936f9e8-b2b7-4d92-9c22-938e6520e421","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-25T06:30:04.490Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7c5225c4-44c6-466f-9a3f-bca9aae5c4c9","controller":"twitter","action":"getTwitterAccounts","userId":"130"}
{"timestamp":"2025-07-25T06:30:04.497Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bdcaf266-194c-4fe4-adab-97556c67f852","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-25T06:30:04.505Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"949c047a-701c-4c04-baec-4b1d8e373019","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-25T06:30:04.509Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"949c047a-701c-4c04-baec-4b1d8e373019","userId":"130"}
{"timestamp":"2025-07-25T06:30:04.516Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":130,"pagesCount":0}
{"timestamp":"2025-07-25T06:30:04.520Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":130,"accountCount":0}
{"timestamp":"2025-07-25T06:30:04.522Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"7c5225c4-44c6-466f-9a3f-bca9aae5c4c9","userId":"130","accountCount":0}
{"timestamp":"2025-07-25T06:30:04.530Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":1}
{"timestamp":"2025-07-25T06:30:04.533Z","level":"INFO","message":"LinkedIn pages retrieved successfully","environment":"DEVELOPMENT","requestId":"bdcaf266-194c-4fe4-adab-97556c67f852","userId":"130","pagesCount":1}
{"timestamp":"2025-07-25T06:30:04.537Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":0}
{"timestamp":"2025-07-25T06:30:08.195Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9b0adea8-e1c6-48d4-aac6-d19da588e9c1","controller":"business","action":"businessList","userId":"130"}
{"timestamp":"2025-07-25T06:30:08.198Z","level":"INFO","message":"Fetching business list","environment":"DEVELOPMENT","requestId":"9b0adea8-e1c6-48d4-aac6-d19da588e9c1","userId":"130"}
{"timestamp":"2025-07-25T06:30:08.247Z","level":"INFO","message":"Business list fetched successfully","environment":"DEVELOPMENT","requestId":"9b0adea8-e1c6-48d4-aac6-d19da588e9c1","userId":"130","businessCount":0}
{"timestamp":"2025-07-25T06:30:22.914Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1fffb118-ca93-439b-831e-e05b8f91eb39","controller":"linkedin","action":"createPost"}
{"timestamp":"2025-07-25T06:30:22.967Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":1}
{"timestamp":"2025-07-25T06:30:24.115Z","level":"WARN","message":"Could not get LinkedIn profile for posting, using fallback","environment":"DEVELOPMENT","error":"Request failed with status code 403"}
{"timestamp":"2025-07-25T06:30:24.117Z","level":"INFO","message":"Media provided but skipping for LinkedIn post","environment":"DEVELOPMENT","mediaCount":1,"profileId":"0d22d0f57406e19c089c6940b7617b57"}
{"timestamp":"2025-07-25T06:30:24.120Z","level":"INFO","message":"Attempting to create LinkedIn post","environment":"DEVELOPMENT","profileId":"0d22d0f57406e19c089c6940b7617b57","postPayload":"{\n  \"author\": \"urn:li:person:~\",\n  \"lifecycleState\": \"PUBLISHED\",\n  \"specificContent\": {\n    \"com.linkedin.ugc.ShareContent\": {\n      \"shareCommentary\": {\n        \"text\": \"test test test test test\"\n      },\n      \"shareMediaCategory\": \"NONE\"\n    }\n  },\n  \"visibility\": {\n    \"com.linkedin.ugc.MemberNetworkVisibility\": \"PUBLIC\"\n  }\n}"}
{"timestamp":"2025-07-25T06:30:24.478Z","level":"ERROR","message":"Error creating LinkedIn post","environment":"DEVELOPMENT","error":"Request failed with status code 422","response":{"message":"ERROR :: /author :: \"urn:li:person:~\" does not match urn:li:company:\\d+|urn:li:member:\\d+\n","status":422},"profileId":"0d22d0f57406e19c089c6940b7617b57","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at LinkedInService.createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\linkedin.service.js:318:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\linkedin.controller.js:421:24)"}
{"timestamp":"2025-07-25T06:30:28.866Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"938d9ee2-64cb-4796-b41a-6c0758350788","controller":"linkedin","action":"createPost"}
{"timestamp":"2025-07-25T06:30:28.896Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":130,"accountsCount":1}
{"timestamp":"2025-07-25T06:30:29.234Z","level":"WARN","message":"Could not get LinkedIn profile for posting, using fallback","environment":"DEVELOPMENT","error":"Request failed with status code 403"}
{"timestamp":"2025-07-25T06:30:29.238Z","level":"INFO","message":"Media provided but skipping for LinkedIn post","environment":"DEVELOPMENT","mediaCount":1,"profileId":"0d22d0f57406e19c089c6940b7617b57"}
{"timestamp":"2025-07-25T06:30:29.242Z","level":"INFO","message":"Attempting to create LinkedIn post","environment":"DEVELOPMENT","profileId":"0d22d0f57406e19c089c6940b7617b57","postPayload":"{\n  \"author\": \"urn:li:person:~\",\n  \"lifecycleState\": \"PUBLISHED\",\n  \"specificContent\": {\n    \"com.linkedin.ugc.ShareContent\": {\n      \"shareCommentary\": {\n        \"text\": \"test test test test test\"\n      },\n      \"shareMediaCategory\": \"NONE\"\n    }\n  },\n  \"visibility\": {\n    \"com.linkedin.ugc.MemberNetworkVisibility\": \"PUBLIC\"\n  }\n}"}
{"timestamp":"2025-07-25T06:30:29.776Z","level":"ERROR","message":"Error creating LinkedIn post","environment":"DEVELOPMENT","error":"Request failed with status code 422","response":{"message":"ERROR :: /author :: \"urn:li:person:~\" does not match urn:li:company:\\d+|urn:li:member:\\d+\n","status":422},"profileId":"0d22d0f57406e19c089c6940b7617b57","stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at LinkedInService.createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\linkedin.service.js:318:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async createPost (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\linkedin.controller.js:421:24)"}
