const axios = require("axios");
const logger = require("../utils/logger");

class LinkedInService {
  constructor() {
    this.clientId = process.env.LINKEDIN_CLIENT_ID || "7736hsd2hmgkaq";
    this.clientSecret =
      process.env.LINKEDIN_CLIENT_SECRET || "WPL_AP1.uXQYwKtx41AtBChz.IHWgdw==";
    this.redirectUri =
      process.env.LINKEDIN_REDIRECT_URI ||
      `${
        process.env.BACKEND_URL || "http://localhost:3000"
      }/v1/linkedin/callback`;
    this.baseUrl = "https://api.linkedin.com/v2";
    this.authUrl = "https://www.linkedin.com/oauth/v2";
  }

  /**
   * Validate LinkedIn configuration
   * @returns {boolean} Configuration validity
   */
  validateConfig() {
    const isValid = !!(this.clientId && this.clientSecret && this.redirectUri);

    if (!isValid) {
      logger.error("LinkedIn configuration is incomplete", {
        hasClientId: !!this.clientId,
        hasClientSecret: !!this.clientSecret,
        hasRedirectUri: !!this.redirectUri,
      });
    }

    return isValid;
  }

  /**
   * Generate LinkedIn OAuth URL
   * @param {number} userId - User ID
   * @returns {string} OAuth URL
   */
  generateAuthUrl(userId) {
    try {
      const state = JSON.stringify({ userId });
      const scopes = ["openid", "profile", "w_member_social", "email"];

      const params = new URLSearchParams({
        response_type: "code",
        client_id: this.clientId,
        redirect_uri: this.redirectUri,
        state: state,
        scope: scopes.join(" "),
      });

      const authUrl = `${this.authUrl}/authorization?${params.toString()}`;

      logger.info("LinkedIn OAuth URL generated", {
        userId,
        scopes: scopes.join(", "),
      });

      return authUrl;
    } catch (error) {
      logger.error("Error generating LinkedIn OAuth URL", {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Exchange authorization code for access token
   * @param {string} code - Authorization code
   * @returns {Promise<Object>} Token data
   */
  async exchangeCodeForToken(code) {
    try {
      const tokenUrl = `${this.authUrl}/accessToken`;

      const params = new URLSearchParams({
        grant_type: "authorization_code",
        code: code,
        redirect_uri: this.redirectUri,
        client_id: this.clientId,
        client_secret: this.clientSecret,
      });

      const response = await axios.post(tokenUrl, params, {
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
      });

      logger.info("LinkedIn access token obtained successfully", {
        hasAccessToken: !!response.data.access_token,
        expiresIn: response.data.expires_in,
      });

      return response.data;
    } catch (error) {
      logger.error("Error exchanging LinkedIn code for token", {
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get LinkedIn user profile
   * @param {string} accessToken - Access token
   * @returns {Promise<Object>} User profile data
   */
  async getUserProfile(accessToken) {
    try {
      // With only w_member_social scope, we have limited profile access
      // We'll create a minimal profile with the user ID from the token
      let profile = { id: null };

      try {
        // Try to get basic profile info using LinkedIn API v2
        const profileResponse = await axios.get(`${this.baseUrl}/me`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });
        debugger;
        profile = profileResponse.data;
      } catch (profileError) {
        // If profile access fails, generate a unique ID from the token
        logger.info("Limited profile access with w_member_social scope", {
          error: profileError.message,
        });
        // Use a hash of the access token as a unique identifier
        const crypto = require("crypto");
        profile.id = crypto
          .createHash("md5")
          .update(accessToken.substring(0, 20))
          .digest("hex");
      }

      // Get email address using the email scope
      let emailAddress = null;
      try {
        const emailResponse = await axios.get(
          `${this.baseUrl}/emailAddress?q=members&projection=(elements*(handle~))`,
          {
            headers: {
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );
        const emailData = emailResponse.data;
        emailAddress = emailData.elements?.[0]?.["handle~"]?.emailAddress;
        logger.info("Email retrieved successfully for LinkedIn user", {
          userId: profile.id,
          hasEmail: !!emailAddress,
        });
      } catch (emailError) {
        logger.error("Error getting LinkedIn user email", {
          userId: profile.id,
          error: emailError.message,
        });
      }

      const userProfile = {
        id: profile.id,
        localizedFirstName:
          profile.localizedFirstName ||
          profile.firstName?.localized?.en_US ||
          "LinkedIn",
        localizedLastName:
          profile.localizedLastName ||
          profile.lastName?.localized?.en_US ||
          "User",
        emailAddress: emailAddress,
        profilePicture: profile.profilePicture?.displayImage || null,
      };

      logger.info("LinkedIn user profile retrieved successfully", {
        userId: profile.id,
        hasEmail: !!emailAddress,
        fullProfile: JSON.stringify(profile, null, 2),
      });

      return userProfile;
    } catch (error) {
      logger.error("Error getting LinkedIn user profile", {
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Create LinkedIn post
   * @param {Object} postData - Post data
   * @returns {Promise<Object>} Post creation result
   */
  async createPost(postData) {
    try {
      const {
        accessToken,
        profileId,
        text,
        media,
        published,
        scheduledPublishTime,
      } = postData;

      // Get the current user's profile to get the correct member ID
      let authorUrn = null; // No fallback - we need a valid URN

      try {
        // Use the newer /userInfo endpoint instead of deprecated /me
        const profileResponse = await axios.get(`${this.baseUrl}/userInfo`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });

        if (profileResponse.data && profileResponse.data.sub) {
          // The userInfo endpoint returns 'sub' field which is the user ID
          const userId = profileResponse.data.sub;
          authorUrn = `urn:li:person:${userId}`;

          logger.info("Retrieved LinkedIn user ID for posting", {
            userId,
            authorUrn,
          });
        } else {
          throw new Error("No user ID found in userInfo response");
        }
      } catch (profileError) {
        logger.error("Could not get LinkedIn profile for posting", {
          error: profileError.message,
          response: profileError.response?.data,
        });
        throw new Error(
          `Failed to get LinkedIn user profile: ${profileError.message}`
        );
      }

      if (!authorUrn) {
        throw new Error("Could not determine LinkedIn user URN for posting");
      }

      const postPayload = {
        author: authorUrn,
        lifecycleState: published ? "PUBLISHED" : "DRAFT",
        specificContent: {
          "com.linkedin.ugc.ShareContent": {
            shareCommentary: {
              text: text,
            },
            shareMediaCategory: "NONE", // Always NONE for now
          },
        },
        visibility: {
          "com.linkedin.ugc.MemberNetworkVisibility": "PUBLIC",
        },
      };

      // Add media if provided
      if (media && media.length > 0) {
        // For now, we'll skip media upload and create text-only posts
        // LinkedIn requires media to be uploaded to their servers first
        // This is a complex process that requires additional API calls
        logger.info("Media provided but skipping for LinkedIn post", {
          mediaCount: media.length,
          profileId,
        });

        // Set to NONE for now until we implement proper media upload
        postPayload.specificContent[
          "com.linkedin.ugc.ShareContent"
        ].shareMediaCategory = "NONE";
      }

      // Add scheduled publish time if provided
      if (scheduledPublishTime && !published) {
        postPayload.publishedAt = new Date(scheduledPublishTime).getTime();
      }

      logger.info("Attempting to create LinkedIn post", {
        profileId,
        postPayload: JSON.stringify(postPayload, null, 2),
      });

      const response = await axios.post(
        `${this.baseUrl}/ugcPosts`,
        postPayload,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
            "X-Restli-Protocol-Version": "2.0.0",
          },
        }
      );

      const postId = response.data.id;

      logger.info("LinkedIn post created successfully", {
        profileId,
        postId,
        published,
        hasMedia: !!(media && media.length > 0),
      });

      return {
        success: true,
        postId: postId,
        response: response.data,
      };
    } catch (error) {
      logger.error("Error creating LinkedIn post", {
        error: error.message,
        response: error.response?.data,
        profileId: postData.profileId,
      });

      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to create LinkedIn post",
        error: error.response?.data || error.message,
      };
    }
  }

  /**
   * Upload media to LinkedIn
   * @param {string} accessToken - Access token
   * @param {string} profileId - Profile ID
   * @param {Buffer} mediaBuffer - Media buffer
   * @param {string} mediaType - Media type
   * @returns {Promise<Object>} Upload result
   */
  async uploadMedia(accessToken, profileId, mediaBuffer, mediaType) {
    try {
      // Register upload
      const registerPayload = {
        registerUploadRequest: {
          recipes: ["urn:li:digitalmediaRecipe:feedshare-image"],
          owner: `urn:li:person:${profileId}`,
          serviceRelationships: [
            {
              relationshipType: "OWNER",
              identifier: "urn:li:userGeneratedContent",
            },
          ],
        },
      };

      const registerResponse = await axios.post(
        `${this.baseUrl}/assets?action=registerUpload`,
        registerPayload,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            "Content-Type": "application/json",
          },
        }
      );

      const uploadUrl =
        registerResponse.data.value.uploadMechanism[
          "com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest"
        ].uploadUrl;
      const asset = registerResponse.data.value.asset;

      // Upload media
      await axios.put(uploadUrl, mediaBuffer, {
        headers: {
          "Content-Type": mediaType,
        },
      });

      logger.info("LinkedIn media uploaded successfully", {
        profileId,
        asset,
        mediaType,
      });

      return {
        success: true,
        asset: asset,
        uploadUrl: uploadUrl,
      };
    } catch (error) {
      logger.error("Error uploading LinkedIn media", {
        error: error.message,
        response: error.response?.data,
        profileId,
      });
      throw error;
    }
  }
}

module.exports = LinkedInService;
