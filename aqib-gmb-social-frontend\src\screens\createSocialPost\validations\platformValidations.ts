import * as yup from "yup";
import {
  TOPIC_TYPES,
  EVENT_TYPES,
} from "../../../constants/application.constant";

// Domain regex for URL validation
const domainRegex =
  /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;

// Google Post Validation Schema
export const GooglePostValidationSchema = yup.object().shape({
  event: yup
    .object()
    .nullable()
    .when("$topicType", (topicType, schema) => {
      if (
        topicType &&
        (topicType[0] === TOPIC_TYPES.Event ||
          topicType[0] === TOPIC_TYPES.Offer)
      ) {
        return schema.nonNullable().required("Event is required");
      }
      return schema; // Keep it nullable for other types
    })
    .shape({
      title: yup
        .string()
        .nullable()
        .transform((value) => (value === "" ? null : value))
        .when("$topicType", (topicType, schema) => {
          if (topicType && topicType[0] === TOPIC_TYPES.Event) {
            return schema.nonNullable().required("Title is required");
          }
          return schema; // Keep it nullable for other types
        }),
      schedule: yup.object().shape({
        startTime: yup
          .string()
          .nullable()
          .when("$topicType", (topicType, schema) => {
            if (
              topicType &&
              (topicType[0] === TOPIC_TYPES.Event ||
                topicType[0] === TOPIC_TYPES.Offer)
            ) {
              return schema.nonNullable().required("Start Time is required");
            }
            return schema; // Keep it nullable for other types
          }),
        endTime: yup
          .string()
          .nullable()
          .when("$topicType", (topicType, schema) => {
            if (
              topicType &&
              (topicType[0] === TOPIC_TYPES.Event ||
                topicType[0] === TOPIC_TYPES.Offer)
            ) {
              return schema.nonNullable().required("End Time is required");
            }
            return schema; // Keep it nullable for other types
          }),
      }),
    }),
  summary: yup
    .string()
    .min(10, "Summary must be at least 10 characters")
    .max(1500, "Summary cannot exceed 1500 characters")
    .required("Summary is required"),
  media: yup
    .array()
    .min(1, "At least one image is required")
    .required("Media is required"),
  callToAction: yup
    .object()
    .nullable()
    .when("$hasCallToAction", (hasCallToAction, schema) => {
      if (hasCallToAction && hasCallToAction[0]) {
        return schema.shape({
          actionType: yup.string().required("Action type is required"),
          url: yup
            .string()
            .nullable()
            .when("actionType", (actionType, schema) => {
              if (actionType && actionType[0] !== EVENT_TYPES.CallNow) {
                return schema
                  .nonNullable()
                  .matches(domainRegex, "Invalid domain format")
                  .required("Url is required");
              }
              return schema; // Keep it nullable for other types
            }),
        });
      }
      return schema.nullable();
    }),
  offer: yup
    .object()
    .nullable()
    .when("$topicType", (topicType, schema) => {
      if (topicType && topicType[0] === TOPIC_TYPES.Offer) {
        return schema.shape({
          couponCode: yup.string().required("Coupon code is required"),
          redeemOnlineUrl: yup
            .string()
            .nullable()
            .when("$hasCallToAction", (hasCallToAction, schema) => {
              if (hasCallToAction && hasCallToAction[0]) {
                return schema
                  .nonNullable()
                  .matches(domainRegex, "Invalid domain format")
                  .required("Url is required");
              }
              return schema; // Keep it nullable for other types
            }),
          termsConditions: yup
            .string()
            .required("Terms and conditions are required"),
        });
      }
      return schema; // Keep it nullable for other types
    }),
  topicType: yup.string().required("Topic type is required"),
});

// Facebook Post Validation Schema
export const FacebookPostValidationSchema = yup.object().shape({
  message: yup
    .string()
    .min(10, "Message must be at least 10 characters")
    .max(2000, "Message cannot exceed 2000 characters")
    .required("Message is required"),
  link: yup
    .string()
    .nullable()
    .when("$hasLink", (hasLink, schema) => {
      if (hasLink && hasLink[0]) {
        return schema
          .nonNullable()
          .matches(domainRegex, "Invalid URL format")
          .required("Link is required");
      }
      return schema;
    }),
  selectedPages: yup
    .array()
    .min(1, "Please select at least one Facebook page")
    .required("Facebook pages are required"),
});

// Instagram Post Validation Schema
export const InstagramPostValidationSchema = yup.object().shape({
  caption: yup
    .string()
    .min(5, "Caption must be at least 5 characters")
    .max(2200, "Caption cannot exceed 2200 characters")
    .required("Caption is required"),
  media: yup
    .array()
    .min(1, "At least one image is required")
    .max(10, "Maximum 10 images allowed")
    .required("Media is required"),
  hashtags: yup.array().max(30, "Maximum 30 hashtags allowed"),
  selectedAccounts: yup
    .array()
    .min(1, "Please select at least one Instagram account")
    .required("Instagram accounts are required"),
});

// LinkedIn Post Validation Schema
export const LinkedInPostValidationSchema = yup.object().shape({
  text: yup
    .string()
    .min(10, "Text must be at least 10 characters")
    .max(3000, "Text cannot exceed 3000 characters")
    .required("Text is required"),
  title: yup.string().max(200, "Title cannot exceed 200 characters"),
  description: yup
    .string()
    .max(300, "Description cannot exceed 300 characters"),
  link: yup
    .string()
    .nullable()
    .when("$hasLink", (hasLink, schema) => {
      if (hasLink && hasLink[0]) {
        return schema
          .nonNullable()
          .matches(domainRegex, "Invalid URL format")
          .required("Link is required");
      }
      return schema;
    }),
  selectedProfiles: yup
    .array()
    .min(1, "Please select at least one LinkedIn profile")
    .required("LinkedIn profiles are required"),
});

// Platform validation mapping
export const PlatformValidations = {
  google: GooglePostValidationSchema,
  facebook: FacebookPostValidationSchema,
  instagram: InstagramPostValidationSchema,
  linkedin: LinkedInPostValidationSchema,
};

// Platform validation function
export const validatePlatformData = async (platform: string, data: any) => {
  const schema =
    PlatformValidations[platform as keyof typeof PlatformValidations];
  if (!schema) {
    throw new Error(`Validation schema not found for platform: ${platform}`);
  }
  return await schema.validate(data, { abortEarly: false });
};
