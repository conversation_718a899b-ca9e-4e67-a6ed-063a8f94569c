{"name": "express-js-aws-lambda-claudia-boilerplate", "version": "1.0.0", "description": "Backend boilerplate code built using Express.js(Node.js)", "main": "app.local.js", "scripts": {"devStart": "nodemon app.local.js", "dev": "NODE_ENV=development nodemon app.local.js", "staging": "NODE_ENV=staging nodemon app.local.js", "production": "NODE_ENV=production node app.local.js", "start:dev": "NODE_ENV=development node app.local.js", "start:staging": "NODE_ENV=staging node app.local.js", "start:production": "NODE_ENV=production node app.local.js", "dev:win": "set NODE_ENV=development && nodemon app.local.js", "staging:win": "set NODE_ENV=staging && nodemon app.local.js", "production:win": "set NODE_ENV=production && node app.local.js", "start:dev:win": "set NODE_ENV=development && node app.local.js", "start:staging:win": "set NODE_ENV=staging && node app.local.js", "start:production:win": "set NODE_ENV=production && node app.local.js", "validate:env": "node scripts/validate-env.js", "validate:env:dev": "node scripts/validate-env.js development", "validate:env:staging": "node scripts/validate-env.js staging", "validate:env:production": "node scripts/validate-env.js production", "env-setup-dev": "node setup/envToJson.js DEV", "env-setup-production": "node setup/envToJson.js PROD", "setup:manage-assets": "node scripts/setup-manage-assets.js", "fix:manage-assets-table": "node scripts/fix-manage-assets-table.js", "setup:s3-bucket": "node scripts/setup-s3-bucket.js", "add:thumbnail-support": "node scripts/add-thumbnail-support.js", "generate-proxy": "claudia generate-serverless-express-proxy --express-module app", "clean-old-zips": "powershell -Command \"Get-ChildItem -Path . -Filter 'deployment-*.zip' | Remove-Item -Force\"", "zip": "powershell -Command \"$timestamp = Get-Date -Format 'yyyyMMddHHmmss'; Remove-Item -Path 'deployment-*.zip' -ErrorAction Ignore; $excludePatterns = @('node_modules', '.env*', '*.env', '*.config','*.config.*', 'configs', '.git', '.gitignore', '*.gitignore', 'deployment-*.zip', '*.log', 'logs', 'temp', 'tmp', '.DS_Store', 'Thumbs.db', 'package-lock.json'); $items = Get-ChildItem -Path . | Where-Object { $name = $_.Name; -not ($excludePatterns | Where-Object { $name -like $_ }) }; Compress-Archive -Path $items.FullName -DestinationPath (\\\"deployment-aqib-gmb-social-backend-$timestamp.zip\\\") -CompressionLevel Optimal\"", "start": "ntl", "test:db": "node database/test_connection.js", "test:postLocations": "node database/test_postLocations.js", "migrate:gmb-locations": "node database/run_migration.js", "rollback:gmb-locations": "node database/run_rollback.js", "add-logging": "node scripts/add-logging-to-controllers.js", "add-comprehensive-logging": "node scripts/add-comprehensive-logging.js", "implement-all-logging": "node scripts/implement-all-logging.js", "fix:instagram-status": "node scripts/fix-instagram-status.js"}, "repository": {"type": "git", "url": "git+https://github.com/anveshadicherla001/express-js-aws-lambda-claudia-boilerplate.git"}, "keywords": ["Node.js", "Express.js", "Community", "Backend"], "author": "<PERSON><PERSON><PERSON>", "license": "GPL-3.0-only", "bugs": {"url": "https://github.com/anveshadicherla001/express-js-aws-lambda-claudia-boilerplate/issues"}, "homepage": "https://github.com/anveshadicherla001/express-js-aws-lambda-claudia-boilerplate#readme", "dependencies": {"@apidevtools/swagger-parser": "^11.0.0", "@google/generative-ai": "^0.21.0", "ajv": "^8.16.0", "async": "^3.2.6", "aws-sdk": "^2.1692.0", "aws-serverless-express": "^3.4.0", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "exceljs": "^4.4.0", "express": "^4.19.2", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "google-auth-library": "^9.15.1", "googleapis": "^144.0.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "mysql2": "^3.10.0", "rc": "^1.2.8", "sharp": "^0.34.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.1.3"}}