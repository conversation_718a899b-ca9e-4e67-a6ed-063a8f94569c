import React, { useState, useRef } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Alert,
  Chip,
  Grid,
} from "@mui/material";
import {
  CloudUpload as CloudUploadIcon,
  Image as ImageIcon,
  VideoFile as VideoIcon,
} from "@mui/icons-material";

interface FileUploadComponentProps {
  onFileUpload: (files: FileList) => Promise<void>;
  uploading: boolean;
  maxSizeMB: number;
  currentUsageMB: number;
}

const FileUploadComponent: React.FC<FileUploadComponentProps> = ({
  onFileUpload,
  uploading,
  maxSizeMB,
  currentUsageMB,
}) => {
  const [dragOver, setDragOver] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const allowedTypes = [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
    "video/mp4",
    "video/avi",
    "video/mov",
    "video/wmv",
    "video/flv",
    "video/webm",
  ];

  const maxFileSize = 100 * 1024 * 1024; // 100MB per file

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    const files = e.dataTransfer.files;
    handleFileSelection(files);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFileSelection(e.target.files);
    }
  };

  const handleFileSelection = (files: FileList) => {
    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach((file) => {
      // Check file type
      if (!allowedTypes.includes(file.type)) {
        errors.push(`${file.name}: Unsupported file type`);
        return;
      }

      // Check file size
      if (file.size > maxFileSize) {
        errors.push(`${file.name}: File too large (max 100MB)`);
        return;
      }

      validFiles.push(file);
    });

    // Check total storage limit
    const totalNewSize = validFiles.reduce((sum, file) => sum + file.size, 0);
    const totalNewSizeMB = totalNewSize / (1024 * 1024);

    if (currentUsageMB + totalNewSizeMB > maxSizeMB) {
      errors.push(`Upload would exceed storage limit (${maxSizeMB}MB)`);
      return;
    }

    if (errors.length > 0) {
      // Show errors (you might want to use toast here)
      console.error("File validation errors:", errors);
      return;
    }

    setSelectedFiles(validFiles);
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    const fileList = new DataTransfer();
    selectedFiles.forEach((file) => fileList.items.add(file));

    await onFileUpload(fileList.files);
    setSelectedFiles([]);

    // Clear file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileIcon = (file: File) => {
    return file.type.startsWith("image/") ? <ImageIcon /> : <VideoIcon />;
  };

  return (
    <Card sx={{ marginBottom: 3 }}>
      <CardContent>
        {/* Drag and Drop Area */}
        <Box
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
          sx={{
            border: `2px dashed ${dragOver ? "#1976d2" : "#ccc"}`,
            borderRadius: 2,
            padding: 4,
            textAlign: "center",
            cursor: "pointer",
            backgroundColor: dragOver ? "#f5f5f5" : "transparent",
            transition: "all 0.3s ease",
            marginBottom: 2,
          }}
        >
          <CloudUploadIcon
            sx={{ fontSize: 48, color: "#1976d2", marginBottom: 1 }}
          />
          <Typography variant="h6" gutterBottom>
            {dragOver
              ? "Drop files here"
              : "Drag & drop files or click to browse"}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Supported: Images (JPG, PNG, GIF, WebP) and Videos (MP4, AVI, MOV,
            WMV, FLV, WebM)
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Max file size: 100MB | Available space:{" "}
            {(maxSizeMB - currentUsageMB).toFixed(2)}MB
          </Typography>
        </Box>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={allowedTypes.join(",")}
          onChange={handleFileInputChange}
          style={{ display: "none" }}
        />

        {/* Selected Files */}
        {selectedFiles.length > 0 && (
          <Box marginBottom={2}>
            <Typography variant="subtitle1" gutterBottom>
              Selected Files ({selectedFiles.length})
            </Typography>
            <Grid container spacing={1}>
              {selectedFiles.map((file, index) => (
                <Grid item xs={12} sm={6} md={4} key={index}>
                  <Chip
                    icon={getFileIcon(file)}
                    label={`${file.name} (${formatFileSize(file.size)})`}
                    onDelete={() => removeFile(index)}
                    variant="outlined"
                    sx={{ width: "100%", justifyContent: "flex-start" }}
                  />
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* Upload Progress */}
        {uploading && (
          <Box marginBottom={2}>
            <Typography variant="body2" gutterBottom>
              Uploading files... Please wait
            </Typography>
            <LinearProgress />
          </Box>
        )}

        {/* Upload Button */}
        <Button
          variant="contained"
          startIcon={<CloudUploadIcon />}
          onClick={handleUpload}
          disabled={selectedFiles.length === 0 || uploading}
          fullWidth
          sx={{ marginTop: 1, textTransform:"capitalize", borderRadius:"5px" }}
        >
          {uploading
            ? "Uploading..."
            : `Upload ${selectedFiles.length} File${
                selectedFiles.length !== 1 ? "s" : ""
              }`}
        </Button>

        {/* Storage Warning */}
        {currentUsageMB / maxSizeMB > 0.8 && (
          <Alert severity="warning" sx={{ marginTop: 2 }}>
            Storage is {((currentUsageMB / maxSizeMB) * 100).toFixed(1)}% full.
            Consider deleting unused assets or contact support to increase your
            storage limit.
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default FileUploadComponent;
