{"timestamp":"2025-07-24T14:26:00.871Z","level":"INFO","message":"Starting Instagram token status fix...","environment":"development"}
{"timestamp":"2025-07-24T14:26:00.899Z","level":"ERROR","message":"Error fixing Instagram token status:","environment":"development","error":"","stack":"AggregateError\n    at internalConnectMultiple (node:net:1114:18)\n    at afterConnectMultiple (node:net:1667:5)"}
{"timestamp":"2025-07-24T14:27:49.529Z","level":"INFO","message":"Starting Instagram token status fix...","environment":"development"}
{"timestamp":"2025-07-24T14:27:49.560Z","level":"ERROR","message":"Error fixing Instagram token status:","environment":"development","error":"","stack":"AggregateError\n    at internalConnectMultiple (node:net:1114:18)\n    at afterConnectMultiple (node:net:1667:5)"}
{"timestamp":"2025-07-24T14:42:53.233Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-24T14:42:53.291Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-24T14:42:57.535Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-24T09:13:00.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-24T14:46:20.634Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"aadac1c0-f697-4720-8b82-f842f04c43d2","controller":"auth","action":"login","email":"<EMAIL>"}
{"timestamp":"2025-07-24T14:46:20.636Z","level":"INFO","message":"Login attempt","environment":"DEVELOPMENT","requestId":"aadac1c0-f697-4720-8b82-f842f04c43d2","email":"<EMAIL>","ip":"::1"}
{"timestamp":"2025-07-24T14:46:21.062Z","level":"INFO","message":"Login successful","environment":"DEVELOPMENT","requestId":"aadac1c0-f697-4720-8b82-f842f04c43d2","userId":132,"email":"<EMAIL>"}
{"timestamp":"2025-07-24T14:46:23.291Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-24","endDate":"2025-07-24"}
{"timestamp":"2025-07-24T14:46:23.411Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-24T14:46:26.720Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ee34d246-fe28-494f-83f3-88e4fac56b92","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-24T14:46:26.729Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"76beccb0-3096-4f45-b96b-78240ddd9f71","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-24T14:46:26.742Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bfb1df0b-b05f-4d70-9cc8-b07c91dee604","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-24T14:46:26.755Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e2e9bc25-c182-4870-b131-0565e820fcbe","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-24T14:46:26.760Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"e2e9bc25-c182-4870-b131-0565e820fcbe","userId":"132"}
{"timestamp":"2025-07-24T14:46:26.804Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-24T14:46:26.900Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:46:26.905Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-24T14:46:26.907Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"bfb1df0b-b05f-4d70-9cc8-b07c91dee604","userId":"132","accountCount":0}
{"timestamp":"2025-07-24T14:46:26.925Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:46:28.537Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bb081c09-56ee-4d3a-ade6-58167952b338","controller":"instagram","action":"authenticate"}
{"timestamp":"2025-07-24T14:46:28.539Z","level":"INFO","message":"Instagram authentication initiated","environment":"DEVELOPMENT","requestId":"bb081c09-56ee-4d3a-ade6-58167952b338","userId":132}
{"timestamp":"2025-07-24T14:46:28.545Z","level":"INFO","message":"Instagram OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts"}
{"timestamp":"2025-07-24T14:46:28.547Z","level":"INFO","message":"Instagram authentication URL generated","environment":"DEVELOPMENT","requestId":"bb081c09-56ee-4d3a-ade6-58167952b338","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-24T14:46:35.229Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ce170353-16ba-4e6a-b47c-054abb0f5e9a","controller":"instagram","action":"callback"}
{"timestamp":"2025-07-24T14:46:37.983Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e82c9bff-5c63-408e-943b-301f711ac4b7","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T14:46:37.985Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"e82c9bff-5c63-408e-943b-301f711ac4b7","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T14:46:37.987Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"e82c9bff-5c63-408e-943b-301f711ac4b7","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T14:46:37.989Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"e82c9bff-5c63-408e-943b-301f711ac4b7","userId":132}
{"timestamp":"2025-07-24T14:46:38.379Z","level":"INFO","message":"Instagram (Facebook) access token obtained","environment":"DEVELOPMENT","hasAccessToken":true,"tokenType":"bearer"}
{"timestamp":"2025-07-24T14:46:38.765Z","level":"INFO","message":"Facebook user info retrieved for Instagram","environment":"DEVELOPMENT","userId":"****************","hasEmail":true}
{"timestamp":"2025-07-24T14:46:38.767Z","level":"INFO","message":"Instagram user info retrieved","environment":"DEVELOPMENT","requestId":"e82c9bff-5c63-408e-943b-301f711ac4b7","userId":"****************","userName":"Sean Sunny","hasEmail":true}
{"timestamp":"2025-07-24T14:46:53.648Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","totalPages":1}
{"timestamp":"2025-07-24T14:46:58.535Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"18e3d643-93d9-4ced-94b0-4833e4270666","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T14:46:58.550Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"18e3d643-93d9-4ced-94b0-4833e4270666","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T14:46:58.560Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"18e3d643-93d9-4ced-94b0-4833e4270666","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T14:46:58.566Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"18e3d643-93d9-4ced-94b0-4833e4270666","userId":132}
{"timestamp":"2025-07-24T14:46:58.884Z","level":"ERROR","message":"Error exchanging code for Instagram token:","environment":"DEVELOPMENT","error":"Request failed with status code 400","response":{"error":{"message":"This authorization code has been used.","type":"OAuthException","code":100,"error_subcode":36009,"fbtrace_id":"Ah-qddBd7orx3e7JazXrCMZ"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at InstagramService.exchangeCodeForToken (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\instagram.service.js:115:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:217:23)"}
{"timestamp":"2025-07-24T14:46:58.888Z","level":"ERROR","message":"Error in Instagram callback validation","environment":"DEVELOPMENT","requestId":"18e3d643-93d9-4ced-94b0-4833e4270666","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InstagramService.exchangeCodeForToken (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\instagram.service.js:99:24)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:217:23)"}
{"timestamp":"2025-07-24T14:48:33.615Z","level":"INFO","message":"Instagram Business accounts retrieved","environment":"DEVELOPMENT","totalPages":1,"instagramAccountsCount":0}
{"timestamp":"2025-07-24T14:48:33.616Z","level":"INFO","message":"Instagram pages retrieved","environment":"DEVELOPMENT","requestId":"e82c9bff-5c63-408e-943b-301f711ac4b7","pagesCount":0,"pageIds":[]}
{"timestamp":"2025-07-24T14:48:33.654Z","level":"INFO","message":"Instagram OAuth tokens saved","environment":"DEVELOPMENT","userId":132,"instagramUserId":"****************","insertId":22}
{"timestamp":"2025-07-24T14:48:33.655Z","level":"INFO","message":"Instagram authentication completed successfully","environment":"DEVELOPMENT","requestId":"e82c9bff-5c63-408e-943b-301f711ac4b7","userId":132,"instagramUserId":"****************","instagramUserEmail":"<EMAIL>","accountsCount":0}
{"timestamp":"2025-07-24T14:48:40.311Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"270f8020-33ed-410e-91f6-349e189b4a78","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-24T14:48:40.317Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a726eef8-b5e3-4aa4-83ec-72781c9cb75a","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-24T14:48:40.326Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"71a5a816-3036-4b39-9241-8a468459ba9c","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-24T14:48:40.329Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"71a5a816-3036-4b39-9241-8a468459ba9c","userId":"132"}
{"timestamp":"2025-07-24T14:48:40.335Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d4f48eb1-758a-412e-afd9-64a12a2ed98b","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-24T14:48:40.343Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-24T14:48:40.357Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:48:40.364Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:48:40.378Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-24T14:48:40.381Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"d4f48eb1-758a-412e-afd9-64a12a2ed98b","userId":"132","accountCount":0}
{"timestamp":"2025-07-24T14:48:42.026Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d3a86aac-6ddd-4c70-b387-6421ce57187f","controller":"instagram","action":"authenticate"}
{"timestamp":"2025-07-24T14:48:42.029Z","level":"INFO","message":"Instagram authentication initiated","environment":"DEVELOPMENT","requestId":"d3a86aac-6ddd-4c70-b387-6421ce57187f","userId":132}
{"timestamp":"2025-07-24T14:48:42.032Z","level":"INFO","message":"Instagram OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts"}
{"timestamp":"2025-07-24T14:48:42.034Z","level":"INFO","message":"Instagram authentication URL generated","environment":"DEVELOPMENT","requestId":"d3a86aac-6ddd-4c70-b387-6421ce57187f","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-24T14:48:45.879Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"25234b59-98ef-4259-8e39-c0804b7290d0","controller":"instagram","action":"callback"}
{"timestamp":"2025-07-24T14:48:48.665Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b4315991-97d0-46df-b03a-12364ef55b74","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T14:48:48.668Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"b4315991-97d0-46df-b03a-12364ef55b74","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T14:48:48.670Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"b4315991-97d0-46df-b03a-12364ef55b74","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T14:48:48.674Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"b4315991-97d0-46df-b03a-12364ef55b74","userId":132}
{"timestamp":"2025-07-24T14:48:49.108Z","level":"INFO","message":"Instagram (Facebook) access token obtained","environment":"DEVELOPMENT","hasAccessToken":true,"tokenType":"bearer"}
{"timestamp":"2025-07-24T14:48:49.520Z","level":"INFO","message":"Facebook user info retrieved for Instagram","environment":"DEVELOPMENT","userId":"****************","hasEmail":true}
{"timestamp":"2025-07-24T14:48:49.522Z","level":"INFO","message":"Instagram user info retrieved","environment":"DEVELOPMENT","requestId":"b4315991-97d0-46df-b03a-12364ef55b74","userId":"****************","userName":"Sean Sunny","hasEmail":true}
{"timestamp":"2025-07-24T14:48:52.310Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","totalPages":1}
{"timestamp":"2025-07-24T14:48:52.643Z","level":"INFO","message":"Instagram Business accounts retrieved","environment":"DEVELOPMENT","totalPages":1,"instagramAccountsCount":0}
{"timestamp":"2025-07-24T14:48:52.644Z","level":"INFO","message":"Instagram pages retrieved","environment":"DEVELOPMENT","requestId":"b4315991-97d0-46df-b03a-12364ef55b74","pagesCount":0,"pageIds":[]}
{"timestamp":"2025-07-24T14:48:52.677Z","level":"INFO","message":"Instagram OAuth tokens saved","environment":"DEVELOPMENT","userId":132,"instagramUserId":"****************","insertId":22}
{"timestamp":"2025-07-24T14:48:52.679Z","level":"INFO","message":"Instagram authentication completed successfully","environment":"DEVELOPMENT","requestId":"b4315991-97d0-46df-b03a-12364ef55b74","userId":132,"instagramUserId":"****************","instagramUserEmail":"<EMAIL>","accountsCount":0}
{"timestamp":"2025-07-24T14:48:57.077Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7d153ab2-6501-4593-a39e-6b84fbb379ae","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-24T14:48:57.080Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"7d153ab2-6501-4593-a39e-6b84fbb379ae","userId":"132"}
{"timestamp":"2025-07-24T14:48:57.088Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"fca3065a-9f8f-4874-967a-2c459a179d41","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-24T14:48:57.096Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"702b3f08-ce27-4497-aa7d-201b1f0760ae","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-24T14:48:57.104Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0f5ef9ff-93df-4b23-ab3e-9fb6f332fd12","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-24T14:48:57.123Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:48:57.132Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-24T14:48:57.134Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"0f5ef9ff-93df-4b23-ab3e-9fb6f332fd12","userId":"132","accountCount":0}
{"timestamp":"2025-07-24T14:48:57.145Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:48:57.212Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-24T14:49:27.397Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d931c764-afdd-43ee-a9d3-851af4fcb34a","controller":"instagram","action":"authenticate"}
{"timestamp":"2025-07-24T14:49:27.399Z","level":"INFO","message":"Instagram authentication initiated","environment":"DEVELOPMENT","requestId":"d931c764-afdd-43ee-a9d3-851af4fcb34a","userId":132}
{"timestamp":"2025-07-24T14:49:27.401Z","level":"INFO","message":"Instagram OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts"}
{"timestamp":"2025-07-24T14:49:27.403Z","level":"INFO","message":"Instagram authentication URL generated","environment":"DEVELOPMENT","requestId":"d931c764-afdd-43ee-a9d3-851af4fcb34a","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-24T14:49:32.362Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2eac53bb-95ca-4af2-867a-43433347ac22","controller":"instagram","action":"callback"}
{"timestamp":"2025-07-24T14:49:35.154Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d5e3a95b-0d1c-48c1-baf9-e1c43cfbdc19","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T14:49:35.157Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"d5e3a95b-0d1c-48c1-baf9-e1c43cfbdc19","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T14:49:35.160Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"d5e3a95b-0d1c-48c1-baf9-e1c43cfbdc19","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T14:49:35.162Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"d5e3a95b-0d1c-48c1-baf9-e1c43cfbdc19","userId":132}
{"timestamp":"2025-07-24T14:49:35.684Z","level":"INFO","message":"Instagram (Facebook) access token obtained","environment":"DEVELOPMENT","hasAccessToken":true,"tokenType":"bearer"}
{"timestamp":"2025-07-24T14:49:36.067Z","level":"INFO","message":"Facebook user info retrieved for Instagram","environment":"DEVELOPMENT","userId":"****************","hasEmail":true}
{"timestamp":"2025-07-24T14:49:36.069Z","level":"INFO","message":"Instagram user info retrieved","environment":"DEVELOPMENT","requestId":"d5e3a95b-0d1c-48c1-baf9-e1c43cfbdc19","userId":"****************","userName":"Sean Sunny","hasEmail":true}
{"timestamp":"2025-07-24T14:49:38.872Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","totalPages":1}
{"timestamp":"2025-07-24T14:49:39.213Z","level":"INFO","message":"Instagram Business accounts retrieved","environment":"DEVELOPMENT","totalPages":1,"instagramAccountsCount":0}
{"timestamp":"2025-07-24T14:49:39.215Z","level":"INFO","message":"Instagram pages retrieved","environment":"DEVELOPMENT","requestId":"d5e3a95b-0d1c-48c1-baf9-e1c43cfbdc19","pagesCount":0,"pageIds":[]}
{"timestamp":"2025-07-24T14:49:39.247Z","level":"INFO","message":"Instagram OAuth tokens saved","environment":"DEVELOPMENT","userId":132,"instagramUserId":"****************","insertId":25}
{"timestamp":"2025-07-24T14:49:39.249Z","level":"INFO","message":"Instagram authentication completed successfully","environment":"DEVELOPMENT","requestId":"d5e3a95b-0d1c-48c1-baf9-e1c43cfbdc19","userId":132,"instagramUserId":"****************","instagramUserEmail":"<EMAIL>","accountsCount":0}
{"timestamp":"2025-07-24T14:49:43.154Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"962c03d5-88e2-4f8d-8d40-d6969d776c72","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-24T14:49:43.166Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e715fa53-0d1a-4b85-b376-ef06935b7745","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-24T14:49:43.169Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"e715fa53-0d1a-4b85-b376-ef06935b7745","userId":"132"}
{"timestamp":"2025-07-24T14:49:43.178Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e5e9454c-4f0a-444b-86f5-77bea1bc39a4","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-24T14:49:43.187Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"6c644f99-418c-4f4b-95e4-13af0c2921a8","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-24T14:49:43.197Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-24T14:49:43.207Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:49:43.215Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:49:43.226Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-24T14:49:43.231Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"6c644f99-418c-4f4b-95e4-13af0c2921a8","userId":"132","accountCount":0}
{"timestamp":"2025-07-24T14:50:00.887Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"649e61ea-9479-4bbe-8619-feec8bbe0b7d","controller":"instagram","action":"authenticate"}
{"timestamp":"2025-07-24T14:50:00.889Z","level":"INFO","message":"Instagram authentication initiated","environment":"DEVELOPMENT","requestId":"649e61ea-9479-4bbe-8619-feec8bbe0b7d","userId":132}
{"timestamp":"2025-07-24T14:50:00.893Z","level":"INFO","message":"Instagram OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts"}
{"timestamp":"2025-07-24T14:50:00.895Z","level":"INFO","message":"Instagram authentication URL generated","environment":"DEVELOPMENT","requestId":"649e61ea-9479-4bbe-8619-feec8bbe0b7d","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-24T14:50:04.078Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b4769140-09c8-4ac5-b116-35b46b0b2e8c","controller":"instagram","action":"callback"}
{"timestamp":"2025-07-24T14:50:06.813Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f5e47248-0263-4730-bb93-f9479bc003ae","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T14:50:06.815Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"f5e47248-0263-4730-bb93-f9479bc003ae","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T14:50:06.817Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"f5e47248-0263-4730-bb93-f9479bc003ae","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T14:50:06.819Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"f5e47248-0263-4730-bb93-f9479bc003ae","userId":132}
{"timestamp":"2025-07-24T14:50:07.198Z","level":"INFO","message":"Instagram (Facebook) access token obtained","environment":"DEVELOPMENT","hasAccessToken":true,"tokenType":"bearer"}
{"timestamp":"2025-07-24T14:50:07.616Z","level":"INFO","message":"Facebook user info retrieved for Instagram","environment":"DEVELOPMENT","userId":"****************","hasEmail":true}
{"timestamp":"2025-07-24T14:50:07.617Z","level":"INFO","message":"Instagram user info retrieved","environment":"DEVELOPMENT","requestId":"f5e47248-0263-4730-bb93-f9479bc003ae","userId":"****************","userName":"Sean Sunny","hasEmail":true}
{"timestamp":"2025-07-24T14:50:14.095Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","totalPages":1}
{"timestamp":"2025-07-24T14:50:14.524Z","level":"INFO","message":"Instagram Business accounts retrieved","environment":"DEVELOPMENT","totalPages":1,"instagramAccountsCount":0}
{"timestamp":"2025-07-24T14:50:14.527Z","level":"INFO","message":"Instagram pages retrieved","environment":"DEVELOPMENT","requestId":"f5e47248-0263-4730-bb93-f9479bc003ae","pagesCount":0,"pageIds":[]}
{"timestamp":"2025-07-24T14:50:14.558Z","level":"INFO","message":"Instagram OAuth tokens saved","environment":"DEVELOPMENT","userId":132,"instagramUserId":"****************","insertId":25}
{"timestamp":"2025-07-24T14:50:14.560Z","level":"INFO","message":"Instagram authentication completed successfully","environment":"DEVELOPMENT","requestId":"f5e47248-0263-4730-bb93-f9479bc003ae","userId":132,"instagramUserId":"****************","instagramUserEmail":"<EMAIL>","accountsCount":0}
{"timestamp":"2025-07-24T14:50:19.135Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"1b2a2b1a-4382-4d9d-a668-c0615b281f9e","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-24T14:50:19.141Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d4313394-1daa-4fa7-b962-01e0dcb5b46b","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-24T14:50:19.143Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"d4313394-1daa-4fa7-b962-01e0dcb5b46b","userId":"132"}
{"timestamp":"2025-07-24T14:50:19.149Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9fe96442-be7e-474f-bb82-f833c3086ef3","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-24T14:50:19.155Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"9fa5e191-132c-4e20-beec-ff393e5725f3","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-24T14:50:19.160Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-24T14:50:19.171Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:50:19.175Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:50:19.184Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-24T14:50:19.186Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"9fa5e191-132c-4e20-beec-ff393e5725f3","userId":"132","accountCount":0}
{"timestamp":"2025-07-24T14:50:31.707Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"07fc1787-8bb4-425c-a3eb-907bc4903677","controller":"instagram","action":"authenticate"}
{"timestamp":"2025-07-24T14:50:31.710Z","level":"INFO","message":"Instagram authentication initiated","environment":"DEVELOPMENT","requestId":"07fc1787-8bb4-425c-a3eb-907bc4903677","userId":132}
{"timestamp":"2025-07-24T14:50:31.712Z","level":"INFO","message":"Instagram OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts"}
{"timestamp":"2025-07-24T14:50:31.714Z","level":"INFO","message":"Instagram authentication URL generated","environment":"DEVELOPMENT","requestId":"07fc1787-8bb4-425c-a3eb-907bc4903677","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-24T14:50:38.391Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"50d40a85-ef4a-47fd-be41-63b4285cf48b","controller":"instagram","action":"callback"}
{"timestamp":"2025-07-24T14:50:41.130Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"07adc0ea-6e42-44b7-a90d-1d1c221598c4","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T14:50:41.134Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"07adc0ea-6e42-44b7-a90d-1d1c221598c4","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T14:50:41.137Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"07adc0ea-6e42-44b7-a90d-1d1c221598c4","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T14:50:41.142Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"07adc0ea-6e42-44b7-a90d-1d1c221598c4","userId":132}
{"timestamp":"2025-07-24T14:50:41.548Z","level":"INFO","message":"Instagram (Facebook) access token obtained","environment":"DEVELOPMENT","hasAccessToken":true,"tokenType":"bearer"}
{"timestamp":"2025-07-24T14:50:41.925Z","level":"INFO","message":"Facebook user info retrieved for Instagram","environment":"DEVELOPMENT","userId":"****************","hasEmail":true}
{"timestamp":"2025-07-24T14:50:41.927Z","level":"INFO","message":"Instagram user info retrieved","environment":"DEVELOPMENT","requestId":"07adc0ea-6e42-44b7-a90d-1d1c221598c4","userId":"****************","userName":"Sean Sunny","hasEmail":true}
{"timestamp":"2025-07-24T14:50:54.256Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","totalPages":1}
{"timestamp":"2025-07-24T14:50:54.652Z","level":"INFO","message":"Instagram Business accounts retrieved","environment":"DEVELOPMENT","totalPages":1,"instagramAccountsCount":0}
{"timestamp":"2025-07-24T14:50:54.655Z","level":"INFO","message":"Instagram pages retrieved","environment":"DEVELOPMENT","requestId":"07adc0ea-6e42-44b7-a90d-1d1c221598c4","pagesCount":0,"pageIds":[]}
{"timestamp":"2025-07-24T14:50:54.690Z","level":"INFO","message":"Instagram OAuth tokens saved","environment":"DEVELOPMENT","userId":132,"instagramUserId":"****************","insertId":25}
{"timestamp":"2025-07-24T14:50:54.693Z","level":"INFO","message":"Instagram authentication completed successfully","environment":"DEVELOPMENT","requestId":"07adc0ea-6e42-44b7-a90d-1d1c221598c4","userId":132,"instagramUserId":"****************","instagramUserEmail":"<EMAIL>","accountsCount":0}
{"timestamp":"2025-07-24T14:50:59.120Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"f3cff00e-4b5f-4b35-855f-9b864eff2d89","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-24T14:50:59.126Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"c5bec9ea-158f-4771-91e1-abc8bf037b19","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-24T14:50:59.129Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"c5bec9ea-158f-4771-91e1-abc8bf037b19","userId":"132"}
{"timestamp":"2025-07-24T14:50:59.136Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"34497e91-e4de-4761-88c8-23ad11327f05","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-24T14:50:59.142Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"87e589eb-7f7b-4db9-b859-89a29e6876ad","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-24T14:50:59.148Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-24T14:50:59.155Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:50:59.162Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:50:59.168Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-24T14:50:59.170Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"87e589eb-7f7b-4db9-b859-89a29e6876ad","userId":"132","accountCount":0}
{"timestamp":"2025-07-24T14:51:01.995Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"fc8cc94c-9ffb-4011-a34d-08346e223b28","controller":"instagram","action":"authenticate"}
{"timestamp":"2025-07-24T14:51:01.997Z","level":"INFO","message":"Instagram authentication initiated","environment":"DEVELOPMENT","requestId":"fc8cc94c-9ffb-4011-a34d-08346e223b28","userId":132}
{"timestamp":"2025-07-24T14:51:02.000Z","level":"INFO","message":"Instagram OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts"}
{"timestamp":"2025-07-24T14:51:02.002Z","level":"INFO","message":"Instagram authentication URL generated","environment":"DEVELOPMENT","requestId":"fc8cc94c-9ffb-4011-a34d-08346e223b28","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-24T14:51:07.229Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"307bcb87-f381-4231-813c-f0b75c5cdd0d","controller":"instagram","action":"callback"}
{"timestamp":"2025-07-24T14:51:09.929Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"2e655212-7b08-4ca6-9a33-9f170ddfd98b","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T14:51:09.932Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"2e655212-7b08-4ca6-9a33-9f170ddfd98b","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T14:51:09.935Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"2e655212-7b08-4ca6-9a33-9f170ddfd98b","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T14:51:09.937Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"2e655212-7b08-4ca6-9a33-9f170ddfd98b","userId":132}
{"timestamp":"2025-07-24T14:51:10.501Z","level":"INFO","message":"Instagram (Facebook) access token obtained","environment":"DEVELOPMENT","hasAccessToken":true,"tokenType":"bearer"}
{"timestamp":"2025-07-24T14:51:10.987Z","level":"INFO","message":"Facebook user info retrieved for Instagram","environment":"DEVELOPMENT","userId":"****************","hasEmail":true}
{"timestamp":"2025-07-24T14:51:10.990Z","level":"INFO","message":"Instagram user info retrieved","environment":"DEVELOPMENT","requestId":"2e655212-7b08-4ca6-9a33-9f170ddfd98b","userId":"****************","userName":"Sean Sunny","hasEmail":true}
{"timestamp":"2025-07-24T14:51:17.886Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","totalPages":1}
{"timestamp":"2025-07-24T14:51:39.576Z","level":"INFO","message":"Instagram Business accounts retrieved","environment":"DEVELOPMENT","totalPages":1,"instagramAccountsCount":0}
{"timestamp":"2025-07-24T14:51:43.013Z","level":"INFO","message":"Instagram pages retrieved","environment":"DEVELOPMENT","requestId":"2e655212-7b08-4ca6-9a33-9f170ddfd98b","pagesCount":0,"pageIds":[]}
{"timestamp":"2025-07-24T14:51:44.153Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"8cb9b167-cf5d-4bb5-aa9f-6969094a98d4","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T14:51:44.155Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"8cb9b167-cf5d-4bb5-aa9f-6969094a98d4","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T14:51:44.158Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"8cb9b167-cf5d-4bb5-aa9f-6969094a98d4","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T14:51:44.159Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"8cb9b167-cf5d-4bb5-aa9f-6969094a98d4","userId":132}
{"timestamp":"2025-07-24T14:51:44.178Z","level":"INFO","message":"Instagram OAuth tokens saved","environment":"DEVELOPMENT","userId":132,"instagramUserId":"****************","insertId":25}
{"timestamp":"2025-07-24T14:51:50.279Z","level":"INFO","message":"Instagram authentication completed successfully","environment":"DEVELOPMENT","requestId":"2e655212-7b08-4ca6-9a33-9f170ddfd98b","userId":132,"instagramUserId":"****************","instagramUserEmail":"<EMAIL>","accountsCount":0}
{"timestamp":"2025-07-24T14:51:53.296Z","level":"ERROR","message":"Error exchanging code for Instagram token:","environment":"DEVELOPMENT","error":"Request failed with status code 400","response":{"error":{"message":"This authorization code has been used.","type":"OAuthException","code":100,"error_subcode":36009,"fbtrace_id":"AjHBjtwRURb-sTJljX82etp"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at InstagramService.exchangeCodeForToken (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\instagram.service.js:115:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:217:23)"}
{"timestamp":"2025-07-24T14:51:53.297Z","level":"ERROR","message":"Error in Instagram callback validation","environment":"DEVELOPMENT","requestId":"8cb9b167-cf5d-4bb5-aa9f-6969094a98d4","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InstagramService.exchangeCodeForToken (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\instagram.service.js:99:24)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:217:23)"}
{"timestamp":"2025-07-24T14:52:03.165Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"********-c41d-4797-8d5d-a84c481de3a9","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-24T14:52:03.173Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"a6d818ee-8bb2-48bc-b56b-d2cc74f68ff1","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-24T14:52:03.176Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"a6d818ee-8bb2-48bc-b56b-d2cc74f68ff1","userId":"132"}
{"timestamp":"2025-07-24T14:52:03.182Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"23d10137-f7ca-46f8-99f8-fc9ea1cf6e58","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-24T14:52:03.194Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"da10f736-9a2b-4c65-b439-25bd98e3202e","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-24T14:52:03.204Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-24T14:52:03.213Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:52:03.218Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-24T14:52:03.220Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"23d10137-f7ca-46f8-99f8-fc9ea1cf6e58","userId":"132","accountCount":0}
{"timestamp":"2025-07-24T14:52:03.227Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T14:52:10.713Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"874cd1a8-acb3-46f8-b3c3-6b24fa5c8e53","controller":"instagram","action":"authenticate"}
{"timestamp":"2025-07-24T14:52:10.715Z","level":"INFO","message":"Instagram authentication initiated","environment":"DEVELOPMENT","requestId":"874cd1a8-acb3-46f8-b3c3-6b24fa5c8e53","userId":132}
{"timestamp":"2025-07-24T14:52:10.717Z","level":"INFO","message":"Instagram OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts"}
{"timestamp":"2025-07-24T14:52:10.719Z","level":"INFO","message":"Instagram authentication URL generated","environment":"DEVELOPMENT","requestId":"874cd1a8-acb3-46f8-b3c3-6b24fa5c8e53","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-24T14:52:14.104Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"0581eb06-6dc5-44dc-b368-9a16376fff81","controller":"instagram","action":"callback"}
{"timestamp":"2025-07-24T14:52:16.862Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"734b3660-942a-4e94-aad5-e9b683837ec7","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T14:52:16.864Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"734b3660-942a-4e94-aad5-e9b683837ec7","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T14:52:16.866Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"734b3660-942a-4e94-aad5-e9b683837ec7","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T14:52:16.870Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"734b3660-942a-4e94-aad5-e9b683837ec7","userId":132}
{"timestamp":"2025-07-24T14:52:17.317Z","level":"INFO","message":"Instagram (Facebook) access token obtained","environment":"DEVELOPMENT","hasAccessToken":true,"tokenType":"bearer"}
{"timestamp":"2025-07-24T14:52:17.690Z","level":"INFO","message":"Facebook user info retrieved for Instagram","environment":"DEVELOPMENT","userId":"****************","hasEmail":true}
{"timestamp":"2025-07-24T14:52:17.694Z","level":"INFO","message":"Instagram user info retrieved","environment":"DEVELOPMENT","requestId":"734b3660-942a-4e94-aad5-e9b683837ec7","userId":"****************","userName":"Sean Sunny","hasEmail":true}
{"timestamp":"2025-07-24T14:52:23.477Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","totalPages":1}
{"timestamp":"2025-07-24T15:00:50.702Z","level":"INFO","message":"Instagram Business accounts retrieved","environment":"DEVELOPMENT","totalPages":1,"instagramAccountsCount":0}
{"timestamp":"2025-07-24T15:00:50.704Z","level":"INFO","message":"Instagram pages retrieved","environment":"DEVELOPMENT","requestId":"734b3660-942a-4e94-aad5-e9b683837ec7","pagesCount":0,"pageIds":[]}
{"timestamp":"2025-07-24T15:00:50.739Z","level":"INFO","message":"Instagram OAuth tokens saved","environment":"DEVELOPMENT","userId":132,"instagramUserId":"****************","insertId":25}
{"timestamp":"2025-07-24T15:00:50.740Z","level":"INFO","message":"Instagram authentication completed successfully","environment":"DEVELOPMENT","requestId":"734b3660-942a-4e94-aad5-e9b683837ec7","userId":132,"instagramUserId":"****************","instagramUserEmail":"<EMAIL>","accountsCount":0}
{"timestamp":"2025-07-24T15:00:53.079Z","level":"INFO","message":"Fetching location metrics from database","environment":"DEVELOPMENT","locationId":"6066817250999836539","accountId":"102756707311898130422","startDate":"2024-07-24","endDate":"2025-07-24"}
{"timestamp":"2025-07-24T15:00:53.182Z","level":"INFO","message":"Analytics data found in database","environment":"DEVELOPMENT","locationId":"6066817250999836539","metricsCount":7}
{"timestamp":"2025-07-24T15:01:07.475Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-24T15:01:07.521Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-24T15:01:08.353Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-24T09:31:11.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
{"timestamp":"2025-07-24T15:01:13.000Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"4a58f465-0f0f-4214-9bbd-2e35dcd08e51","controller":"facebook","action":"getPages"}
{"timestamp":"2025-07-24T15:01:13.009Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"b937431b-a751-4297-a3fa-d76eafcceedd","controller":"instagram","action":"getAccounts"}
{"timestamp":"2025-07-24T15:01:13.011Z","level":"INFO","message":"Getting Instagram accounts","environment":"DEVELOPMENT","requestId":"b937431b-a751-4297-a3fa-d76eafcceedd","userId":"132"}
{"timestamp":"2025-07-24T15:01:13.030Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"d92d3925-b461-4c3c-8424-b7974a3ee384","controller":"linkedin","action":"getPages"}
{"timestamp":"2025-07-24T15:01:13.039Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"e5cde1cf-b673-481e-b1de-13918c0cbc04","controller":"twitter","action":"getTwitterAccounts","userId":"132"}
{"timestamp":"2025-07-24T15:01:13.050Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","userId":132,"pagesCount":1}
{"timestamp":"2025-07-24T15:01:13.190Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","userId":132,"accountCount":0}
{"timestamp":"2025-07-24T15:01:13.194Z","level":"INFO","message":"Twitter accounts retrieved successfully","environment":"DEVELOPMENT","requestId":"e5cde1cf-b673-481e-b1de-13918c0cbc04","userId":"132","accountCount":0}
{"timestamp":"2025-07-24T15:01:13.201Z","level":"INFO","message":"LinkedIn accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T15:01:13.211Z","level":"INFO","message":"Instagram accounts retrieved","environment":"DEVELOPMENT","userId":132,"accountsCount":0}
{"timestamp":"2025-07-24T15:01:16.808Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"bc686f51-027d-4ac3-8883-a8ec6ce5e613","controller":"instagram","action":"authenticate"}
{"timestamp":"2025-07-24T15:01:16.810Z","level":"INFO","message":"Instagram authentication initiated","environment":"DEVELOPMENT","requestId":"bc686f51-027d-4ac3-8883-a8ec6ce5e613","userId":132}
{"timestamp":"2025-07-24T15:01:16.813Z","level":"INFO","message":"Instagram OAuth URL generated","environment":"DEVELOPMENT","userId":132,"scopes":"public_profile,email,pages_show_list,pages_read_engagement,pages_manage_posts"}
{"timestamp":"2025-07-24T15:01:16.816Z","level":"INFO","message":"Instagram authentication URL generated","environment":"DEVELOPMENT","requestId":"bc686f51-027d-4ac3-8883-a8ec6ce5e613","userId":132,"hasAuthUrl":true}
{"timestamp":"2025-07-24T15:01:20.179Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"7d4b2eac-6d57-4b27-af5b-3ef530874a16","controller":"instagram","action":"callback"}
{"timestamp":"2025-07-24T15:01:22.877Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"ccf64b10-d5b4-4ad3-83dc-9290a758ae10","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T15:01:22.879Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"ccf64b10-d5b4-4ad3-83dc-9290a758ae10","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T15:01:22.881Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"ccf64b10-d5b4-4ad3-83dc-9290a758ae10","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T15:01:22.884Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"ccf64b10-d5b4-4ad3-83dc-9290a758ae10","userId":132}
{"timestamp":"2025-07-24T15:01:23.344Z","level":"INFO","message":"Instagram (Facebook) access token obtained","environment":"DEVELOPMENT","hasAccessToken":true,"tokenType":"bearer"}
{"timestamp":"2025-07-24T15:01:23.744Z","level":"INFO","message":"Facebook user info retrieved for Instagram","environment":"DEVELOPMENT","userId":"****************","hasEmail":true}
{"timestamp":"2025-07-24T15:01:23.745Z","level":"INFO","message":"Instagram user info retrieved","environment":"DEVELOPMENT","requestId":"ccf64b10-d5b4-4ad3-83dc-9290a758ae10","userId":"****************","userName":"Sean Sunny","hasEmail":true}
{"timestamp":"2025-07-24T15:01:33.332Z","level":"INFO","message":"Facebook pages retrieved","environment":"DEVELOPMENT","totalPages":1}
{"timestamp":"2025-07-24T15:01:33.334Z","level":"INFO","message":"Checking page for Instagram Business account","environment":"DEVELOPMENT","pageId":"***************","pageName":"Integration Testers","pageCategory":"Business Consultant","hasPageAccessToken":true}
{"timestamp":"2025-07-24T15:02:21.362Z","level":"INFO","message":"Requesting Instagram business account for page","environment":"DEVELOPMENT","pageId":"***************","url":"https://graph.facebook.com/v20.0/***************","hasAccessToken":true}
{"timestamp":"2025-07-24T15:02:25.842Z","level":"INFO","message":"Controller Action","environment":"DEVELOPMENT","requestId":"66eff123-ebc4-4e9f-a509-27e3ae193eb6","controller":"instagram","action":"callbackValidation"}
{"timestamp":"2025-07-24T15:02:25.844Z","level":"INFO","message":"Decoding state parameter","environment":"DEVELOPMENT","requestId":"66eff123-ebc4-4e9f-a509-27e3ae193eb6","state":"eyJ1c2VySWQiOjEzMn0=","stateLength":20}
{"timestamp":"2025-07-24T15:02:25.851Z","level":"INFO","message":"State decoded successfully","environment":"DEVELOPMENT","requestId":"66eff123-ebc4-4e9f-a509-27e3ae193eb6","decodedState":{"userId":132},"userId":132}
{"timestamp":"2025-07-24T15:02:25.855Z","level":"INFO","message":"Instagram callback validation started","environment":"DEVELOPMENT","requestId":"66eff123-ebc4-4e9f-a509-27e3ae193eb6","userId":132}
{"timestamp":"2025-07-24T15:02:26.198Z","level":"INFO","message":"Instagram business account API response","environment":"DEVELOPMENT","pageId":"***************","responseData":{"id":"***************"},"hasInstagramBusinessAccount":false}
{"timestamp":"2025-07-24T20:09:43.913Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-24T20:09:43.950Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-24T20:09:48.211Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-24T20:10:27.435Z","level":"INFO","message":"Instagram Business account check result","environment":"DEVELOPMENT","pageId":"***************","pageName":"Integration Testers","hasInstagramAccount":false}
{"timestamp":"2025-07-24T20:10:27.456Z","level":"INFO","message":"Instagram Business accounts retrieved","environment":"DEVELOPMENT","totalPages":1,"instagramAccountsCount":0}
{"timestamp":"2025-07-24T20:10:27.458Z","level":"INFO","message":"Instagram pages retrieved","environment":"DEVELOPMENT","requestId":"ccf64b10-d5b4-4ad3-83dc-9290a758ae10","pagesCount":0,"pageIds":[]}
{"timestamp":"2025-07-24T20:10:27.478Z","level":"ERROR","message":"Error saving Instagram OAuth tokens:","environment":"DEVELOPMENT","error":"write ECONNRESET","tokenData":{"userId":132,"instagramUserId":"****************","instagramUserName":"Sean Sunny","instagramUserEmail":"<EMAIL>","instagramUserPicture":"https://platform-lookaside.fbsbx.com/platform/profilepic/?asid=****************&height=50&width=50&ext=**********&hash=AT81P-L7MPX4yDzRQENFiCaZ","accessToken":"[REDACTED]","refreshToken":null,"expiresAt":null},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at Instagram.saveOAuthTokens (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\models\\instagram.models.js:48:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:245:25)"}
{"timestamp":"2025-07-24T20:10:27.481Z","level":"ERROR","message":"Error in Instagram callback validation","environment":"DEVELOPMENT","requestId":"ccf64b10-d5b4-4ad3-83dc-9290a758ae10","error":"write ECONNRESET","stack":"Error: write ECONNRESET\n    at afterWriteDispatched (node:internal/stream_base_commons:160:15)\n    at writeGeneric (node:internal/stream_base_commons:151:3)\n    at Socket._writeGeneric (node:net:952:11)\n    at Socket._write (node:net:964:8)\n    at writeOrBuffer (node:internal/streams/writable:447:12)\n    at _write (node:internal/streams/writable:389:10)\n    at Writable.write (node:internal/streams/writable:393:10)\n    at PoolConnection.write (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\base\\connection.js:256:32)\n    at PoolConnection.writePacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\base\\connection.js:303:12)\n    at Query.start (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\commands\\query.js:61:16)\n    at Query.execute (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\commands\\command.js:45:22)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PoolConnection.addCommand (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\base\\connection.js:500:12)\n    at PoolConnection.query (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\base\\connection.js:571:17)\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\base\\pool.js:149:14\n    at C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\mysql2\\lib\\base\\pool.js:43:37"}
{"timestamp":"2025-07-24T20:10:27.536Z","level":"ERROR","message":"Error exchanging code for Instagram token:","environment":"DEVELOPMENT","error":"Request failed with status code 400","response":{"error":{"message":"This authorization code has been used.","type":"OAuthException","code":100,"error_subcode":36009,"fbtrace_id":"ADUy5Cg_ZRX3kU4DiC6w1jm"}},"stack":"Error\n    at Logger.error (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\utils\\logger.js:98:28)\n    at InstagramService.exchangeCodeForToken (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\instagram.service.js:115:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:217:23)"}
{"timestamp":"2025-07-24T20:10:27.539Z","level":"ERROR","message":"Error in Instagram callback validation","environment":"DEVELOPMENT","requestId":"66eff123-ebc4-4e9f-a509-27e3ae193eb6","error":"Request failed with status code 400","stack":"AxiosError: Request failed with status code 400\n    at settle (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:2049:12)\n    at IncomingMessage.handleStreamEnd (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:3166:11)\n    at IncomingMessage.emit (node:events:526:35)\n    at IncomingMessage.emit (node:domain:488:12)\n    at endReadableNT (node:internal/streams/readable:1408:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Axios.request (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\node_modules\\axios\\dist\\node\\axios.cjs:4276:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async InstagramService.exchangeCodeForToken (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\services\\instagram.service.js:99:24)\n    at async callbackValidation (C:\\Users\\<USER>\\Documents\\Projects\\NabiBaig\\Applications\\aqib-gmb-social-backend\\controllers\\instagram.controller.js:217:23)"}
{"timestamp":"2025-07-24T20:10:50.102Z","level":"INFO","message":"Instagram Service Configuration","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"hasRedirectUri":true,"clientId":"********...","redirectUri":"http://localhost:3000/v1/instagram/callback","apiVersion":"v20.0"}
{"timestamp":"2025-07-24T20:10:50.177Z","level":"INFO","message":"Twitter service initialized","environment":"DEVELOPMENT","hasClientId":true,"hasClientSecret":true,"redirectUri":"http://***********:5173/business-management/twitter/callback","tokenURL":"https://api.x.com/2/oauth2/token","authURL":"https://twitter.com/i/oauth2/authorize"}
{"timestamp":"2025-07-24T20:10:52.581Z","level":"INFO","message":"Application started","environment":"DEVELOPMENT","version":"v1","port":"3000"}
{"timestamp":"2025-07-24T14:40:56.000Z","level":"INFO","message":"Database connected successfully","environment":"DEVELOPMENT","database":"gmb","host":"aqib-gmb-social-reviews.c1c6s60kwqjx.ap-south-1.rds.amazonaws.com"}
