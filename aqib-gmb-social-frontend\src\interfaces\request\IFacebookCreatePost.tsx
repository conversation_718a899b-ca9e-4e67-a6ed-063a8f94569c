export interface IFacebookCreatePost {
  pageId: string;
  pageIds?: string[]; // For multiple page selection
  message: string;
  description?: string;
  link?: string;
  media: IFacebookMedia[];
  published: boolean;
  scheduledPublishTime?: string;
  callToAction?: IFacebookCallToAction;
}

// Interface for bulk Facebook post creation
export interface IFacebookBulkCreatePost {
  pages: IFacebookSelectedPage[];
  message: string;
  description?: string;
  link?: string;
  media: IFacebookMedia[];
  published: boolean;
  scheduledPublishTime?: string;
  callToAction?: IFacebookCallToAction;
}

// Interface for selected Facebook page with post data
export interface IFacebookSelectedPage {
  pageId: string;
  pageName: string;
  pageCategory?: string;
  pagePictureUrl?: string;
  status?: boolean;
  facebookUrl?: string;
  error?: string;
  statusText?: string;
}

export interface IFacebookMedia {
  type: "image" | "video";
  url: string;
  caption?: string;
  sourceUrl?: string;
  mediaFormat?: string;
}

export interface IFacebookCallToAction {
  type: "LEARN_MORE" | "SHOP_NOW" | "BOOK_NOW" | "CALL_NOW" | "SIGN_UP";
  value?: {
    link?: string;
    phone?: string;
  };
}

export interface IFacebookPage {
  id: string;
  name: string;
  access_token: string;
  category?: string;
  picture_url?: string;
  is_active: boolean;
  page_id: string;
  page_name: string;
  page_access_token: string;
  page_category?: string;
  page_picture_url?: string;
}
